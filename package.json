{"name": "khel", "version": "1.0.0", "description": "This is khel sports backend", "main": "index.js", "type": "module", "scripts": {"start": "node --es-module-specifier-resolution=node index", "dev": "nodemon --es-module-specifier-resolution=node", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "KushagraStc", "license": "ISC", "dependencies": {"aws-sdk": "^2.1513.0", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "country-state-city": "^3.2.1", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "fs": "^0.0.1-security", "googleapis": "^129.0.0", "joi": "^17.13.3", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "mongoose": "^7.5.1", "multer": "^1.4.4", "multer-s3": "^2.9.0", "node-cron": "^3.0.3", "path": "^0.12.7", "pdfkit": "^0.15.0", "razorpay": "^2.9.2", "uuid": "^9.0.1", "validator": "^13.11.0"}, "devDependencies": {"nodemon": "^3.0.1"}}