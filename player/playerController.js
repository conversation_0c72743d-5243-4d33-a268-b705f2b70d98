import { Player } from "./playerModal.js";
import { Booking } from "../bookings/bookingModal.js";
import bcrypt from "bcryptjs";
import validator from "validator";
import { newToken } from "../utils/jwt.js";
import { createPlayerWallet } from "../wallet/walletController.js";
import { sendSms, sendEmail } from "../utils/msg.js";
import { Wallet } from "../wallet/walletModal.js";
import { SECRETS } from "../utils/config.js";
import crypto from "crypto";

// LOGIN
export const loginSignUPWithGoogle = async (req, res) => {
  try {
    const { email, firstName, lastName } = req.body;
    const user = await Player.findOne({ email });
    if (user) {
      if (user.loginType === "google") {
        const token = newToken(user, "player");
        return res
          .status(200)
          .json({ status: "ok", token: token, id: user._id });
      } else {
        return res
          .status(400)
          .json({ error: "Please login with email and password" });
      }
    } else {
      const data = await Player.create({
        email,
        firstName,
        lastName,
        loginType: "google",
      });
      const token = newToken(data, "player");
      await createPlayerWallet(email, data._id);

      // sending sms and email
      // await sendSms("664ed8d0d6fc05475e4cdbe2","1", mobile, null, null);
      await sendEmail(
        firstName,
        email,
        "template_23_10_2024_13_10_2",
        `{"User": "${firstName}"}`
      );
      return res.status(200).json({ data: data, token });
    }
  } catch (e) {
    console.log("Error while loginSignUPWithGoogle", e);
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const playerLogin = async (req, res) => {
  try {
    const { email, password } = req.body;
    if (!email || !password) {
      return res.status(400).json({ message: "Please Enter Credentials" });
    }
    const user = await Player.findOne({ email });
    if (!user) {
      return res.status(400).json({ message: "User not registered" });
    } else {
      if (user.loginType === "password") {
        const passwordCompare = await bcrypt.compare(password, user.password);
        if (!passwordCompare) {
          return res
            .status(400)
            .json({ message: "Please try to login with correct credentials" });
        } else {
          const token = newToken(user, "player");
          return res
            .status(200)
            .json({ status: "ok", token: token, id: user._id });
        }
      } else {
        return res.status(400).json({ error: "Try to login with google" });
      }
    }
  } catch (err) {
    console.log(err);
    return res.status(500).json({ error: "Internal server error" });
  }
};

// GET ALL PLAYER
export const getPlayers = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let { page, firstName } = req.query;
    page = page || 1;
    let limitVal = 25;
    let skipValue = (page - 1) * limitVal;
    let query = {};
    if (firstName) {
      query.firstName = { $regex: firstName, $options: "i" };
    }
    const totalResults = await Player.countDocuments(query);
    const data = await Player.find(query)
      .skip(skipValue)
      .limit(limitVal)
      .select("-password")
      .populate({
        path: "hobbies.id", // Assuming 'id' is the field referencing the 'categories' collection
        model: "categories",
      });
    if (!data) {
      return res.status(200).json({ error: "User doesn't exist" });
    }
    res.status(200).json({ data, totalResults, length: data.length });
  } catch (e) {
    res.status(500).json({ error: "Internal server error" });
  }
};

// GET PLAYER BY ID
export const getPlayerById = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ message: "id is required" });
    }
    const data = await Player.findById(id).select("-password").populate({
      path: "hobbies.id", // Assuming 'id' is the field referencing the 'categories' collection
      model: "categories",
    });
    if (!data) {
      return res.status(200).json({ message: "Player doesn't exist" });
    }
    res.status(200).json(data);
  } catch (e) {
    console.log(e, "test");
    res.status(500).json({ error: "Internal server error" });
  }
};

// Validation Function
const playerValidation = (firstName, lastName, mobile, email, password) => {
  if (firstName && !validator.isLength(firstName, { min: 3 })) {
    return { error: "First name must have at least 3 characters" };
  }

  if (lastName && !validator.isLength(lastName, { min: 3 })) {
    return { error: "Last name must have at least 3 characters" };
  }

  if (mobile && !validator.isMobilePhone(mobile.toString(), "en-IN")) {
    return { error: "Invalid phone number" };
  }

  if (email && !validator.isEmail(email)) {
    return { error: "Invalid email address" };
  }

  if (password) {
    if (!validator.isLength(password, { min: 6 })) {
      return { error: "Password must have at least 6 characters" };
    }
  }
  return null;
};

// SIGNUP / CREATE -- Player
export const createPlayer = async (req, res) => {
  try {
    const {
      firstName,
      lastName,
      mobile,
      email,
      homeState,
      password,
      confirmPassword,
      hobbies,
      schoolName,
    } = req.body;
    if (!firstName || !lastName || !mobile || !email) {
      return res.status(400).json({
        error: "First name, last name, email, and mobile are required fields",
      });
    }
    const validationResult = playerValidation(
      firstName,
      lastName,
      mobile,
      email,
      password
    );

    if (validationResult) {
      return res.status(400).json(validationResult);
    }

    // Check if user already exists
    const user = await Player.find({ $or: [{ email }, { mobile }] });
    if (user.length > 0) {
      return res
        .status(400)
        .json({ error: "Player with same mobile or email already exists" });
    }
    if (password) {
      if (password !== confirmPassword) {
        return res
          .status(400)
          .json({ error: "Password and confirm password should be same" });
      }
    }
    const salt = password ? await bcrypt.genSalt(10) : undefined;
    const secPass = password ? await bcrypt.hash(password, salt) : undefined;

    let addObj = {
      firstName,
      lastName,
      mobile,
      email,
      homeState,
      password: secPass,
      confirmPassword: secPass,
      hobbies,
      schoolName,
      loginType: "password",
    };
    const data = await Player.create(addObj);
    await createPlayerWallet(email, data._id);

    // sending sms and email
    await sendSms("664ed8d0d6fc05475e4cdbe2","1", mobile, null, null);
    await sendEmail(
      firstName,
      email,
      "template_23_10_2024_13_10_2",
      `{"User": "${firstName}"}`
    );

    // Exclude the password field from the response
    const responseData = data.toObject();
    delete responseData.password;

    // Generate and return token (replace 'user' with appropriate data)
    const token = newToken(responseData, "player");

    return res.status(200).json({ data: responseData, token });
  } catch (err) {
    res.status(500).json({ error: "Internal server error" });
  }
};

// UPDATE Player
export const updatePlayer = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ message: "ID is required" });
    }

    const {
      firstName,
      lastName,
      mobile,
      hobbies,
      schoolName,
      grade,
      homeState,
    } = req.body;

    const validationResult = playerValidation(firstName, lastName, mobile);
    if (validationResult) {
      return res.status(400).json(validationResult);
    }

    // Check if the new mobile number already belongs to another user
    if (mobile) {
      const existingUserWithMobile = await Player.findOne({
        _id: { $ne: id }, // Exclude the current document by its ID
        mobile,
      });
      if (existingUserWithMobile) {
        return res
          .status(400)
          .json({ message: "Player with the same mobile already exists" });
      }
    }

    // Only allow updates to fields other than email
    const updateObj = {
      ...(firstName && { firstName }),
      ...(lastName && { lastName }),
      ...(mobile && { mobile }),
      ...(hobbies && { hobbies }),
      ...(schoolName && { schoolName }),
      ...(homeState && { homeState }),
      ...(grade && { grade }),
    };

    const data = await Player.findByIdAndUpdate(id, updateObj, {
      new: true,
    }).populate({
      path: "hobbies.id", // Assuming 'id' is the field referencing the 'categories' collection
      model: "categories",
    });

    if (!data) {
      return res.status(404).json({ message: "Player not found" });
    }

    const responseData = data.toObject();
    delete responseData.password;

    return res.status(200).json(responseData);
  } catch (err) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

// delete player
export const deletePlayer = async (req, res) => {
  try {
    const id = req.params.id;
    const booking = await Booking.find({ player: id });
    if (booking.length > 0) {
      return res
        .status(400)
        .json({ error: "Can't delete a player with booking" });
    }
    await Player.findByIdAndDelete(id);
    await Wallet.findOneAndDelete({ playerId: id });
    return res.status(200).json({ message: "Player deleted successfully" });
  } catch (e) {
    return res.status(500).json({ error: `Internal server error = ${e}` });
  }
};

// request password reset
export const requestPasswordReset = async (req, res) => {
  try {
    const { email } = req.params;
    const user = await Player.findOne({ email });
    if (!user) {
      return res.status(400).json({ error: "User not found" });
    }
    if (user.loginType === "google") {
      return res.status(400).json({ error: "Try to login with google" });
    }

    const token = crypto.randomBytes(32).toString("hex");
    const tokenExpiry = Date.now() + 3600000; // Token valid for 1 hour

    user.resetPasswordToken = token;
    user.resetPasswordExpires = tokenExpiry;
    await user.save();
    await sendEmail(
      user.firstName,
      email,
      "reset_password_14",
      `{"Name": "${user.firstName}", "Reset_Password_Link":"${SECRETS.playerBaseUrl}/resetPassword?token=${token}"}`
    );
    return res
      .status(200)
      .json({ message: "resent password link sent successfully" });
  } catch (e) {
    return res
      .status(500)
      .json({ error: `Internal server error : ${e.message}` });
  }
};

// reset password
export const resetpassword = async (req, res) => {
  try {
    const { token, newPassword } = req.body;
    const user = await Player.findOne({
      resetPasswordToken: token,
      resetPasswordExpires: { $gt: Date.now() },
    });

    if (!user) {
      return res.status(400).json({ error: "Invalid or expired token" });
    }
    if (
      newPassword &&
      (!validator.isLength(newPassword, { min: 6 }) ||
        typeof newPassword !== "string")
    ) {
      return res.status(400).json({
        error: "Password must be a non-empty string with at least 6 characters",
      });
    }
    const salt = newPassword ? await bcrypt.genSalt(10) : undefined;
    const secPass = newPassword
      ? await bcrypt.hash(newPassword, salt)
      : undefined;

    user.password = secPass; // Make sure to hash the password before saving
    user.confirmPassword = secPass; // Make sure to hash the password before saving
    user.resetPasswordToken = undefined;
    user.resetPasswordExpires = undefined;
    await user.save();

    return res.status(200).json({ message: "Password reset successfully" });
  } catch (e) {
    return res
      .status(500)
      .json({ error: `Internal server error : ${e.message}` });
  }
};
