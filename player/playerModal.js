import mongoose from "mongoose";
const { Schema, model } = mongoose;

const playerSchema = new Schema(
  {
    firstName: {
      type: String,
      required: true,
    },
    lastName: {
      type: String,
    },
    mobile: {
      type: String,
    },
    email: {
      type: String,
      required: true,
      unique: true,
    },
    password: {
      type: String,
    },
    homeState:{
      type: String,
    },
    hobbies: [
      {
        id: {
          type: Schema.Types.ObjectId,
          ref: "categories",
        },
      },
    ],
    schoolName: {
      type: String,
    },
    confirmPassword: {
      type: String,
    },
    loginType: {
      type: "String",
      enum: ["google", "password"],
    },
    resetPasswordToken: {
      type: String,
    },
    resetPasswordExpires: {
      type: Date,
    },
    privacyPolicyAccepted: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

export const Player = model("player", playerSchema);
