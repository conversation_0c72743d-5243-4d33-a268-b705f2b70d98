import { Router } from "express";
import {
  createPlayer,
  playerLogin,
  getPlayers,
  getPlayerById,
  updatePlayer,
  loginSignUPWithGoogle,
  deletePlayer,
  resetpassword,
  requestPasswordReset
} from "./playerController.js";
import { coachProtect, playerProtect } from "../utils/auth.js";
import authenticateUserAccess from "../Helpers/authenticationUser.js";

const router = Router();

router.route("/login").post(playerLogin);
router.route("/google").post(loginSignUPWithGoogle);
router
  .route("/")
  .get(authenticateUserAccess("player", "read"), getPlayers)
  .post(createPlayer);

  
router
  .route("/:id")
  .get(
    authenticateUserAccess("player", "read"),
    playerProtect,
    coachProtect,
    getPlayerById
  )
  .patch(authenticateUserAccess("player", "write"), playerProtect, updatePlayer)
  .delete(
    authenticateUserAccess("player", "delete"),
    playerProtect,
    deletePlayer
  );
router.route("/requestResetPassword/:email").post(requestPasswordReset);
router.route("/resetPassword/").post(resetpassword);

export default router;
