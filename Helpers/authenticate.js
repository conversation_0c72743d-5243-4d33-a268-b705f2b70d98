import bcrypt from "bcryptjs";
import User from "../user-management/User/userModal.js";

async function authenticate(username, password, model) {
  let user = null;
  if (model.toLowerCase() === "user") {
    user = await User.aggregate([
      {
        $match: {
          username: username,
        },  
      },
      {
        $addFields: {
          usergroupids: {
            $map: {
              input: "$userGroup",
              as: "group",
              in: {
                $toObjectId: "$$group",
              },
            },
          },
        },
      },
      {
        $lookup: {
          from: "usergroups",
          localField: "usergroupids",
          foreignField: "_id",
          as: "userGroup",
        },
      },
    ]);
    if (user.length) {
      user = user[0];
    } else {
      user = null;
    }
  } 
  if (!user) {
    return null;
  }
  const isPasswordValid = await bcrypt.compare(password, user.password);
  if (!isPasswordValid) {
    return null;
  }
  let allScopes = formatAccessScopes(user.userGroup);
  let userToBeReturned = {
    _id: user._id,
    name: user.name,
    username: user.username,
    access_scopes: allScopes,
    usergroupids: user.usergroupids,
  };
  return userToBeReturned;
}

export { authenticate };

function formatAccessScopes(userGroup) {
  let access_scopes = {};
  userGroup.forEach((group) => {
    Object.keys(group.access_scopes).forEach((module) => {
      if (access_scopes[module]) {
        let scopes = [...access_scopes[module], ...group.access_scopes[module]];
        scopes = Array.from(new Set(scopes));
        access_scopes[module] = scopes;
      } else {
        access_scopes[module] = group.access_scopes[module];
      }
    });
  });

  return access_scopes;
}