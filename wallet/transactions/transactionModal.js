import mongoose from "mongoose";
const { Schema, model } = mongoose;

const transactionSchema = new Schema(
  {
    email: {
      type: String,
      required: true,
    },
    playerId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "player",
    },
    type: {
      type: String,
      required: true,
      enum: ["credit", "debit", "expire"],
    },
    amount: {
      type: Number,
      required: true,
    },
    walletId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "wallet",
    },
    courseId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "course",
    },
    date: {
      type: Date,
      default: Date.now(),
    },
  },
  { timestamps: true }
);

export const Transactions = model("transactions", transactionSchema);
