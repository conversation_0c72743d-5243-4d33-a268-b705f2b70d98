import validator from "validator";
import { Transactions } from "./transactionModal.js";

// API's
export const getAllTransactions = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let { email, page, playerId, walletId } = req.query;
    page = page || 1;
    let limitVal = 25;
    let skipValue = (page - 1) * limitVal;
    let query = {};
    if (email) {
      query.email = { $regex: email, $options: "i" };
    }
    if (playerId) {
      query.playerId = playerId;
    }
    if (walletId) {
      query.walletId = walletId;
    }
    const totalResults = await Transactions.countDocuments(query);
    const data = await Transactions.find(query)
      .skip(skipValue)
      .limit(limitVal)
      .populate(["playerId", "walletId", "courseId"])
      .sort({ createdAt: -1 });
    return res.status(200).json({ data, totalResults });
  } catch (e) {
    console.log(e);
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const getTransactionById = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "Id is required" });
    }
    const data = await Transactions.findById(id).populate([
      "playerId",
      "walletId",
      "courseId",
    ]);
    if (!data) {
      return res.status(400).json({ error: "Transaction Not found" });
    }
    return res.status(200).json(data);
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const createTransaction = async (params) => {
  try {
    const { email, playerId, type, amount, walletId, courseId } = params;
    if (email && !validator.isEmail(email)) {
      return {
        status: 400,
        error: "Invalid email address",
      };
    }
    if (!playerId || !type || !amount || !walletId || !courseId) {
      return {
        status: 400,
        error: "playerId, type, amount, walletId, courseId",
      };
    }
    const data = await Transactions.create({
      email,
      playerId,
      type,
      amount,
      walletId,
      courseId,
    });
    return {
      status: 200,
      message: "Transaction created successfully",
      data,
    };
  } catch (e) {
    console.log(e, "create transaction error");
    return {
      status: 500,
      error: "Internal Server Error",
    };
  }
};
