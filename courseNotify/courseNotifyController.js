import validator from "validator";
import { Notify } from "./courseNotifyModal.js";

export const getNotify = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let { page, email } = req.query;
    page = page || 1;
    let limitVal = 25;
    let skipValue = (page - 1) * limitVal;
    let query = {};
    if (email) {
      query.email = { $regex: email, $options: "i" };
    }
    const totalResults = await Notify.countDocuments(query);
    const data = await Notify.find(query)
      .skip(skipValue)
      .limit(limitVal)
      .sort({ createdAt: -1 });
    return res.status(200).json({ data, totalResults });
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const createNotify = async (req, res) => {
  try {
    const { email, message } = req.body;
    if (!email || !message) {
      return res.status(400).json({
        error: "email, and message are required fields",
      });
    }
    if (email && !validator.isEmail(email)) {
      return res.status(400).json({ error: "Invalid email address" });
    }
    if (message && !validator.isLength(message, { min: 10 })) {
      return res
        .status(400)
        .json({ error: "message must have at least 6 characters" });
    }
    const data = await Notify.create({
      email,
      message,
    });
    return res.status(200).json(data);
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};
