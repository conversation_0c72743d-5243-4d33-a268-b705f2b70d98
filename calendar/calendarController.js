import { google } from "googleapis";
import { <PERSON> } from "../coaches/coachModal.js";
import axios from "axios";
import { SECRETS } from "../utils/config.js";
import moment from "moment-timezone";

const calendar = google.calendar({
  version: "v3",
  auth: SECRETS.calendarApiKey,
});

const oauth2Client = new google.auth.OAuth2(
  SECRETS.calendarClientId,
  SECRETS.calendarClientSecret,
  SECRETS.calendarRedirectUrl
);

// generate a URL that asks permissions for Blogger and Google Calendar scopes
const scopes = [
  "https://www.googleapis.com/auth/calendar",
  "https://www.googleapis.com/auth/userinfo.email",
];

// Helper function
const isRefreshTokenValid = async (refreshToken) => {
  try {
    oauth2Client.setCredentials({ refresh_token: refreshToken });
    const accessTokenResponse = await oauth2Client.getAccessToken();
    // Ensure token exists in the response
    if (accessTokenResponse && accessTokenResponse.token) {
      return true; // Token is valid
    } else {
      return false; // No token, invalid refresh token
    }
  } catch (error) {
    console.log(error, "Error checking refresh token validity");
    return false;
  }
};
// Helper function
const revokeAccess = async (token) => {
  try {
    const revokeUrl = "https://oauth2.googleapis.com/revoke";
    if (token) {
      const params = new URLSearchParams();
      params.append("token", token);
      params.append("client_id", SECRETS.calendarClientId);
      params.append("client_secret", SECRETS.calendarClientSecret);

      await axios.post(revokeUrl, params);
    } else {
      return "No token found";
    }
  } catch (error) {
    console.error("Error revoking refresh token:", error);
  }
};

// Authenticate google
export const googleAuth = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const state = req.user.email;
    const url = oauth2Client.generateAuthUrl({
      access_type: "offline", // Request refresh token
      scope: scopes,
      state: state,
    });
    return res.status(200).json(url);
  } catch (e) {
    return res.status(500).json(e);
  }
};

// after login google will hit this api
export const googleRedirectURL = async (req, res) => {
  try {
    const { code, state, mobile } = req.query;

    const { tokens } = await oauth2Client.getToken(code);

    const { email } = JSON.parse(
      Buffer.from(tokens.id_token.split(".")[1], "base64").toString()
    );

    // Save the tokens, especially the refresh token
    oauth2Client.setCredentials(tokens);
    const coachDetails = await Coach.findOne({ email: state });

    if (tokens && tokens.refresh_token) {
      const updatedDocument = await Coach.findOneAndUpdate(
        { email: state },
        { $set: { refreshToken: tokens?.refresh_token, googleEmail: email } },
        { new: true }
      );
      if (updatedDocument) {
        if (mobile) {
          return res
            .status(200)
            .json({ message: "Device linked Successfully" });
        }
        return res.redirect(`${SECRETS.coachBaseUrl}`);
      } else {
        return res.status(400).json({
          error: `user not found with the given email: ${state}`,
        });
      }
    } else {
      await revokeAccess(coachDetails?.refreshToken);
      if (mobile) {
        return res.status(400).json({
          error: `Already exists`,
        });
      }
      return res.redirect(`${SECRETS.coachBaseUrl}`);
    }
  } catch (err) {
    return res.status(500).json(err);
  }
};

// create a event
export const scheduleEvent = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const {
      summary,
      description,
      startDateTime,
      endDateTime,
      days,
      attendees,
      daysCount,
      colorId,
    } = req.body;

    const byDayString = days.map((day) => day.substring(0, 2)).join(",");

    const coach = await Coach.findOne({ email: req.user.email });

    if (coach) {
      let token = coach.refreshToken;
      const isValidToken = await isRefreshTokenValid(token);

      if (!isValidToken) {
        await revokeAccess(token);
        await Coach.findOneAndUpdate(
          { email: req.user.email },
          { $set: { refreshToken: "", googleEmail: "" } },
          { new: true }
        );

        return res.status(400).json({
          error: "Not a valid Token",
        });
      }

      oauth2Client.setCredentials({
        refresh_token: `${token}`,
      });

      const data = await calendar.events.insert({
        calendarId: "primary",
        auth: oauth2Client,
        requestBody: {
          summary,
          colorId: colorId ? colorId : "1",
          description,
          start: { dateTime: startDateTime, timeZone: "Asia/Kolkata" },
          end: { dateTime: endDateTime, timeZone: "Asia/Kolkata" },
          recurrence: [
            `RRULE:FREQ=WEEKLY;BYDAY=${byDayString};COUNT=${daysCount}`,
          ],
          attendees,
        },
      });

      return res.status(200).json(data);
    } else {
      return res.status(404).json({
        error: `user not found with the given email: ${req.user.email}`,
      });
    }
  } catch (err) {
    console.log(err);
    return res.status(500).json(err);
  }
};

// get a event
export const getEvent = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const coach = await Coach.findOne({ email: req.user.email });

    if (coach) {
      let token = coach.refreshToken;
      const isValidToken = await isRefreshTokenValid(token);

      if (!isValidToken) {
        await revokeAccess(token);
        await Coach.findOneAndUpdate(
          { email: req.user.email },
          { $set: { refreshToken: "", googleEmail: "" } },
          { new: true }
        );

        return res.status(400).json({
          error: "Not a valid Token",
        });
      }

      oauth2Client.setCredentials({
        refresh_token: `${token}`,
      });
      const eventId = req.params.eventId;

      const event = await calendar.events.get({
        calendarId: "primary",
        eventId: eventId,
        auth: oauth2Client,
      });

      return res.status(200).json({
        data: event.data,
      });
    } else {
      return res.status(404).json({
        error: `User not found with the given email: ${req.user.email}`,
      });
    }
  } catch (err) {
    console.log(err);
    return res.status(500).json({
      error: "Internal server error",
    });
  }
};

// Get list of event
export const getEventList = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }

    let coach;
    // if user is academy user then check if the coach id is valid
    if (req.userType === "academy" && req.user?.academyId?._id) {
      const coachId = req?.body?.coachId;
      if (!coachId) {
        return res
          .status(400)
          .json({ error: "Coach id is required for academy user" });
      }
      console.log("coachId", coachId);
      console.log("academyId", req.user.academyId._id);
      coach = await Coach.findOne({
        _id: coachId,
        academyId: req.user.academyId._id,
      });
      if (!coach) {
        return res.status(400).json({
          error:
            "Coach not found with the given id or you don't have permission to access this coach",
        });
      }
    } else {
      coach = await Coach.findOne({ email: req.user.email });
    }

    if (coach) {
      let token = coach.refreshToken;
      const isValidToken = await isRefreshTokenValid(token);

      if (!isValidToken) {
        await revokeAccess(token);
        await Coach.findOneAndUpdate(
          { email: req.user.email },
          { $set: { refreshToken: "", googleEmail: "" } },
          { new: true }
        );

        return res.status(400).json({
          error: "Not a valid Token",
        });
      }

      oauth2Client.setCredentials({
        refresh_token: `${token}`,
      });

      const isoStartDate = moment
        .tz(req.body.startDate, "Asia/Kolkata")
        .toISOString();
      const isoEndDate = moment
        .tz(req.body.endDate, "Asia/Kolkata")
        .toISOString();
      const events = await calendar.events.list({
        calendarId: "primary",
        auth: oauth2Client,
        timeMin: isoStartDate,
        timeMax: isoEndDate,
        singleEvents: true,
        orderBy: "startTime",
        timeZone: "Asia/Kolkata",
      });

      return res.status(200).json({
        data: events.data.items,
      });
    } else {
      return res.status(400).json({
        error: `User not found with the given email: ${req.user.email}`,
      });
    }
  } catch (err) {
    console.log(err);
    return res.status(500).json({
      error: "Internal Server error",
    });
  }
};

// Update a event
export const updateEvent = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const coach = await Coach.findOne({ email: req.user.email });

    if (coach) {
      let token = coach.refreshToken;
      const isValidToken = await isRefreshTokenValid(token);

      if (!isValidToken) {
        await revokeAccess(token);
        await Coach.findOneAndUpdate(
          { email: req.user.email },
          { $set: { refreshToken: "", googleEmail: "" } },
          { new: true }
        );

        return res.status(400).json({
          error: "Not a valid Token",
        });
      }

      oauth2Client.setCredentials({
        refresh_token: `${token}`,
      });

      const eventId = req.params.eventId;

      const {
        summary,
        description,
        startDateTime,
        endDateTime,
        recurrence,
        attendees,
      } = req.body;

      const updatedEvent = await calendar.events.update({
        calendarId: "primary",
        eventId: eventId,
        auth: oauth2Client,
        requestBody: {
          summary,
          description,
          start: {
            dateTime: startDateTime,
            timeZone: "Asia/Kolkata",
          },
          end: {
            dateTime: endDateTime,
            timeZone: "Asia/Kolkata",
          },
          recurrence,
          attendees,
        },
      });

      return res.status(200).json({
        data: updatedEvent.data,
      });
    } else {
      return res.status(404).json({
        error: `User not found with the given email: ${req.user.email}`,
      });
    }
  } catch (err) {
    console.log(err);
    return res.status(500).json({
      message: "Internal server error",
    });
  }
};

//  delete a event
export const deleteEvent = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const coach = await Coach.findOne({ email: req.user.email });

    if (coach) {
      let token = coach.refreshToken;
      const isValidToken = await isRefreshTokenValid(token);

      if (!isValidToken) {
        await revokeAccess(token);
        await Coach.findOneAndUpdate(
          { email: req.user.email },
          { $set: { refreshToken: "", googleEmail: "" } },
          { new: true }
        );

        return res.status(400).json({
          error: "Not a valid Token",
        });
      }

      oauth2Client.setCredentials({
        refresh_token: `${token}`,
      });

      const eventId = req.params.eventId;

      await calendar.events.delete({
        calendarId: "primary",
        eventId: eventId,
        auth: oauth2Client,
      });

      return res.status(200).json({
        message: "Event deleted successfully",
      });
    } else {
      return res.status(404).json({
        error: `User not found with the given email: ${req.user.email}`,
      });
    }
  } catch (err) {
    console.log(err);
    return res.status(500).json({
      error: "Error deleting event",
    });
  }
};

// get available slots for booking
export const getEventListForBookings = async (params) => {
  try {
    const coach = await Coach.findById(params.id);

    let token = coach.refreshToken;
    const isValidToken = await isRefreshTokenValid(token);
    if (!isValidToken) {
      await revokeAccess(token);
      await Coach.findByIdAndUpdate(
        params.id,
        {
          $set: {
            refreshToken: "",
            googleEmail: "",
          },
        },
        { new: true }
      );
      return {
        status: 400,
        error: "Not a valid token",
      };
    }
    oauth2Client.setCredentials({
      refresh_token: `${token}`,
    });
    const isoStartDate = moment
      .tz(params.startDate, "Asia/Kolkata")
      .toISOString();
    const isoEndDate = moment.tz(params.endDate, "Asia/Kolkata").toISOString();
    let events = await calendar.events.list({
      calendarId: "primary",
      auth: oauth2Client,
      timeMin: isoStartDate,
      timeMax: isoEndDate,
      singleEvents: true,
      orderBy: "startTime",
      timeZone: "Asia/Kolkata",
    });
    return {
      status: 200,
      data: events.data.items,
    };
  } catch (error) {
    return {
      status: 400,
      error,
    };
  }
};

// createCalendarEvent
export const createBookingEvents = async (params) => {
  try {
    const {
      summary,
      description,
      startDateTime,
      endDateTime,
      days,
      attendees,
      daysCount,
      coachId,
      colorId,
    } = params;

    const byDayString = days.map((day) => day.substring(0, 2)).join(",");
    const coachDetails = await Coach.findById(coachId);

    if (!coachDetails) {
      return {
        status: 404, // Not Found
        error: `User not found with ID: ${coachId}`,
      };
    }

    let token = coachDetails.refreshToken;
    const isValidToken = await isRefreshTokenValid(token);
    if (!isValidToken) {
      await revokeAccess(token);
      await Coach.findOneAndUpdate(
        { email: coachDetails.email },
        {
          $set: {
            refreshToken: "",
            googleEmail: "",
          },
        },
        { new: true }
      );
      return {
        status: 400,
        error: "Not a valid token",
      };
    }
    oauth2Client.setCredentials({
      refresh_token: `${token}`,
    });

    const data = await calendar.events.insert({
      calendarId: "primary",
      auth: oauth2Client,
      requestBody: {
        summary,
        description,
        colorId,
        start: {
          dateTime: startDateTime,
          timeZone: "Asia/Kolkata",
        },
        end: {
          dateTime: endDateTime,
          timeZone: "Asia/Kolkata",
        },
        recurrence: [
          `RRULE:FREQ=WEEKLY;BYDAY=${byDayString};COUNT=${daysCount}`,
        ],
        attendees,
      },
    });

    return {
      status: 200,
      data,
    };
  } catch (error) {
    return {
      status: 400,
      error,
    };
  }
};

export const updateBookingEvents = async (params) => {
  try {
    const {
      summary,
      description,
      startDateTime,
      endDateTime,
      days,
      attendees,
      daysCount,
      coachId,
      colorId,
      eventId,
    } = params;
    const byDayString = days.map((day) => day.substring(0, 2)).join(",");

    const coach = await Coach.findById(coachId);

    if (coach) {
      let token = coach.refreshToken;
      const isValidToken = await isRefreshTokenValid(token);

      if (!isValidToken) {
        await revokeAccess(token);
        await Coach.findOneAndUpdate(
          { email: req.user.email },
          { $set: { refreshToken: "", googleEmail: "" } },
          { new: true }
        );

        return {
          status: 400,
          error: "Not a valid token",
        };
      }

      oauth2Client.setCredentials({
        refresh_token: `${token}`,
      });

      const updatedEvent = await calendar.events.update({
        calendarId: "primary",
        eventId: eventId,
        auth: oauth2Client,
        requestBody: {
          summary,
          description,
          colorId,
          start: {
            dateTime: startDateTime,
            timeZone: "Asia/Kolkata",
          },
          end: {
            dateTime: endDateTime,
            timeZone: "Asia/Kolkata",
          },
          recurrence: [
            `RRULE:FREQ=WEEKLY;BYDAY=${byDayString};COUNT=${daysCount}`,
          ],
          attendees,
        },
      });

      return {
        status: 200,
        data: updatedEvent.data,
      };
    } else {
      return {
        status: 404, // Not Found
        error: `User not found with ID: ${coachId}`,
      };
    }
  } catch (e) {
    return {
      status: 400,
      e,
    };
  }
};

//  delete event by params
export const deleteEventByParams = async (coachId, eventId) => {
  try {
    const coach = await Coach.findById(coachId);

    if (coach) {
      let token = coach.refreshToken;
      const isValidToken = await isRefreshTokenValid(token);

      if (!isValidToken) {
        await revokeAccess(token);
        await Coach.findOneAndUpdate(
          { email: coach.email },
          { $set: { refreshToken: "", googleEmail: "" } },
          { new: true }
        );

        return {
          status: 400,
          error: "Not a valid token",
        };
      }

      oauth2Client.setCredentials({
        refresh_token: `${token}`,
      });

      await calendar.events.delete({
        calendarId: "primary",
        eventId: eventId,
        auth: oauth2Client,
      });
      return {
        status: 200,
        message: "Event Deleted Successfully",
      };
    } else {
      return {
        status: 400,
        error: `User not found with the given id: ${coachId}`,
      };
    }
  } catch (err) {
    return {
      status: 500,
      error: "Error deleting event",
    };
  }
};

// delete a single date event
export const deleteSingleEvent = async (coachId, classes) => {
  try {
    const coach = await Coach.findById(coachId);
    if (coach) {
      let token = coach.refreshToken;
      const isValidToken = await isRefreshTokenValid(token);

      if (!isValidToken) {
        await revokeAccess(token);
        await Coach.findOneAndUpdate(
          { email: coach.email },
          { $set: { refreshToken: "", googleEmail: "" } },
          { new: true }
        );

        return {
          status: 400,
          error: "Not a valid token",
        };
      }

      oauth2Client.setCredentials({
        refresh_token: `${token}`,
      });

      for (const classItem of classes) {
        await calendar.events.delete({
          calendarId: "primary",
          eventId: classItem.eventId,
          auth: oauth2Client,
        });
      }

      return {
        status: 200,
        message: "Event Deleted Successfully",
      };
    } else {
      return {
        status: 400,
        error: `Coach not found with Id ${coach.email}`,
      };
    }
  } catch (err) {
    return {
      status: 500,
      error: "Error deleting event",
    };
  }
};
