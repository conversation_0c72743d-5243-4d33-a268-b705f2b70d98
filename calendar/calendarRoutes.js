import { Router } from "express";
import {
  deleteEvent,
  getEvent,
  getEventList,
  googleAuth,
  googleRedirectURL,
  scheduleEvent,
  updateEvent,
} from "./calendarController.js";
import { coachProtect, flexibleAuth } from "../utils/auth.js";

const router = Router();

router.route("/google").get(coachProtect, googleAuth);
router.route("/google/redirect").get(googleRedirectURL);
router
  .route("/eventList")
  .post(flexibleAuth("coach", "read"), coachProtect, getEventList);
router.route("/").post(coachProtect, scheduleEvent);
router
  .route("/:id")
  .get(coachProtect, getEvent)
  .patch(coachProtect, updateEvent)
  .delete(coachProtect, deleteEvent);

export default router;
