import { Router } from "express";
import {
  createAcademyUserGroup,
  getAcademyUserGroupById,
  updateAcademyUserGroup,
  deleteAcademyUserGroup,
  getAllAcademyUserGroups,
} from "./academyUserGroupController.js";
import { validateRequest } from "../../middlewares/validations.js";
import {
  createAcademyUserGroupSchema,
  updateAcademyUserGroupSchema,
} from "./academyUserGroupValidation.js";
import { academyUserProtect } from "../../utils/auth.js";
import { checkAcademyUserAccess } from "../utils/auth.js";

const router = Router();

router
  .route("/")
  .post(
    academyUserProtect(),
    checkAcademyUserAccess("user_group", "write"),
    validateRequest(createAcademyUserGroupSchema),
    createAcademyUserGroup
  )
  .get(
    academyUserProtect(),
    checkAcademyUserAccess("user_group", "read"),
    getAllAcademyUserGroups
  );

router
  .route("/:id")
  .get(academyUserProtect(), checkAcademyUserAccess("user_group", "read"), getAcademyUserGroupById)
  .patch(
    academyUserProtect(),
    checkAcademyUserAccess("user_group", "write"),
    validateRequest(updateAcademyUserGroupSchema),
    updateAcademyUserGroup
  )
  .delete(
    academyUserProtect(),
    checkAcademyUserAccess("user_group", "delete"),
    deleteAcademyUserGroup
  );

export default router;
