import mongoose from "mongoose";

const AcademyUserGroupSchema = new mongoose.Schema(
  {
    academyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "academy",
      required: true,
    },
    name: {
      type: String,
      required: [true, "Name required"],
      validate: {
        validator: function (name) {
          return name.trim().length > 2;
        },
        message: "Name should be of at least 3 characters",
      },
    },
    description: {
      type: String,
    },
    access_scopes: {
      dashboard: {
        type: [String],
        enum: ["read", "write", "delete"],
        default: [],
      },
      coach: {
        type: [String],
        enum: ["read", "write", "delete"],
        default: [],
      },
      course: {
        type: [String],
        enum: ["read", "write", "delete"],
        default: [],
      },
      user: {
        type: [String],
        enum: ["read", "write", "delete"],
        default: [],
      },
      user_group: {
        type: [String],
        enum: ["read", "write", "delete"],
        default: [],
      },
      finances: {
        type: [String],
        enum: ["read", "write", "delete"],
        default: [],
      },
      // Add child modules
      booking: {
        type: [String],
        enum: ["read", "write", "delete"],
        default: [],
      },
      reports: {
        type: [String],
        enum: ["read", "write", "delete"],
        default: [],
      },
      player: {
        type: [String],
        enum: ["read", "write", "delete"],
        default: [],
      },
      cms: {
        type: [String],
        enum: ["read", "write", "delete"],
        default: [],
      },
      contact: {
        type: [String],
        enum: ["read", "write", "delete"],
        default: [],
      },
    },
  },
  { timestamps: true }
);

// Only this middleware is needed
AcademyUserGroupSchema.pre("save", function (next) {
  // Define parent-child relationships
  const parentChildMap = {
    finances: ["booking", "reports"],
  };

  // Sync child permissions with parent
  Object.keys(parentChildMap).forEach((parentModule) => {
    const childModules = parentChildMap[parentModule];
    const parentPermissions = this.access_scopes[parentModule] || [];

    childModules.forEach((childModule) => {
      // Inherit parent permissions
      this.access_scopes[childModule] = [...parentPermissions];
    });
  });

  next();
});

// Add this middleware alongside the pre-save middleware
AcademyUserGroupSchema.pre("findOneAndUpdate", function (next) {
  const update = this.getUpdate();

  if (update.access_scopes) {
    const parentChildMap = {
      finances: ["booking", "reports"],
    };
    Object.keys(parentChildMap).forEach((parentModule) => {
      const childModules = parentChildMap[parentModule];
      const parentPermissions = update.access_scopes[parentModule] || [];

      childModules.forEach((childModule) => {
        update.access_scopes[childModule] = [...parentPermissions];
      });
    });
  }

  next();
});

const AcademyUserGroup = mongoose.model(
  "AcademyUserGroup",
  AcademyUserGroupSchema
);

export default AcademyUserGroup;
