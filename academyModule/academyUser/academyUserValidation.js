import Joi from "joi";

export const academyUserSchema = Joi.object({
  name: Joi.string().min(3).required(),
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
  academyUserGroups: Joi.array().items(Joi.string()).optional(),
  current_user: Joi.optional(),
});

export const updateAcademyUserSchema = Joi.object({
  name: Joi.string().min(3).optional(),
  email: Joi.string().email().optional(),
  password: Joi.string().min(6).optional(),
  current_user: Joi.optional(),
  academyUserGroups: Joi.array().items(Joi.string()).optional(),
});

