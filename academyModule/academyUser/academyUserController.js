import AcademyUser from "./academyUserModal.js";
import AcademyUserGroup from "../academyUserGroup/academyUserGroupModal.js";
import { Academy } from "../academy/academyModel.js";
import { catchAsync } from "../../utils/helpers.js";
import AppError from "../../middlewares/appError.js";
import AppSuccess from "../../middlewares/appSuccess.js";
import bcrypt from "bcryptjs";

// Helper function to create default admin user
export const createDefaultAcademyAdminUser = async (
  academyId,
  adminUserGroupId,
  { name, email, password }
) => {
  try {
    const hashedPassword = await bcrypt.hash(password, 12);

    const adminUser = new AcademyUser({
      academyId,
      name: `${name} Default Admin`,
      email: email.toLowerCase(), // Store email in lowercase
      password: hashedPassword,
      academyUserGroups: [adminUserGroupId],
      isDefaultAdmin: true,
    });
    return adminUser;
  } catch (error) {
    throw new AppError("Failed to create default admin user", 500, {
      errors: [
        {
          field: "adminUser",
          message: "Failed to create default admin user.",
        },
      ],
    });
  }
};

// Create Academy User
export const createAcademyUser = catchAsync(async (req, res, next) => {
  const academyId = req.user.academyId._id;
  const { name, email, password, academyUserGroups } = req.body;

  const existingUser = await AcademyUser.findOne({
    email: email.toLowerCase(),
  });
  if (existingUser) {
    throw new AppError("User with this email already exists", 400, {
      errors: [
        {
          field: "email",
          message: "User with this email already exists.",
        },
      ],
    });
  }
  // Verify academy exists
  const academy = await Academy.findById(academyId);
  if (!academy) {
    throw new AppError("Academy not found", 404, {
      errors: [
        {
          field: "academyId",
          message: "Academy not found.",
        },
      ],
    });
  }

  // Verify user groups exist and belong to the academy
  if (academyUserGroups && academyUserGroups.length > 0) {
    const userGroups = await AcademyUserGroup.find({
      _id: { $in: academyUserGroups },
      academyId,
    });

    if (userGroups.length !== academyUserGroups.length) {
      throw new AppError(
        "One or more user groups not found or don't belong to this academy",
        400,
        {
          errors: [
            {
              field: "academyUserGroups",
              message:
                "Some user group IDs are invalid or do not belong to the specified academy.",
            },
          ],
        }
      );
    }
  } else {
    throw new AppError("User groups are required to create a user", 400, {
      errors: [
        {
          field: "academyUserGroups",
          message: "User groups are required to create a user.",
        },
      ],
    });
  }
  // Check if user with same email already exists (case insensitive)

  // Hash password
  const hashedPassword = await bcrypt.hash(password, 12);

  const user = new AcademyUser({
    academyId,
    name,
    email: email.toLowerCase(), // Store email in lowercase
    password: hashedPassword,
    academyUserGroups: academyUserGroups || [],
  });

  const savedUser = await user.save();

  // Remove password from response
  const userResponse = savedUser.toObject();
  delete userResponse.password;

  return new AppSuccess(res, {
    message: "Academy user created successfully",
    data: userResponse,
    statusCode: 201,
  });
});

// Get Academy Users
export const getAllAcademyUsers = catchAsync(async (req, res, next) => {
  const { page = 1, limit = 10, search } = req.query;
  const academyId = req.user.academyId._id;
  const query = { academyId };
  if (search) {
    query.$or = [
      { name: { $regex: search, $options: "i" } },
      { email: { $regex: search, $options: "i" } },
    ];
  }

  const users = await AcademyUser.find(query)
    .select("-password")
    .populate("academyId", "name email")
    .populate("academyUserGroups", "name")
    .limit(Number(limit))
    .skip((Number(page) - 1) * Number(limit))
    .sort({ createdAt: -1 });

  const total = await AcademyUser.countDocuments(query);

  return new AppSuccess(res, {
    message: "Academy users retrieved successfully",
    data: {
      users,
      totalResults: total,
      length: users.length,
    },
    statusCode: 200,
  });
});

// Get Academy User by ID
export const getAcademyUserById = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const academyId = req.user.academyId._id;

  const user = await AcademyUser.findOne({ _id: id, academyId })
    .select("-password")
    .populate("academyUserGroups", "name description access_scopes")
    .populate({
      path: "academyId",
      select: "name email",
    });
  if (!user) {
    throw new AppError("Academy user not found", 404, {
      errors: [
        {
          field: "id",
          message: "Academy user not found.",
        },
      ],
    });
  }
  return new AppSuccess(res, {
    message: "Academy user retrieved successfully",
    data: {
      user,
    },
    statusCode: 200,
  });
});

// Update Academy User
export const updateAcademyUser = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { name, email, academyUserGroups, password } = req.body;

  const user = await AcademyUser.findById(id);
  if (!user) {
    throw new AppError("Academy user not found", 404, {
      errors: [
        {
          field: "id",
          message: "Academy user not found.",
        },
      ],
    });
  }

  if (user.isDefaultAdmin) {
    throw new AppError("Cannot update the default admin user", 400, {
      errors: [
        {
          field: "isDefaultAdmin",
          message: "Cannot update the default admin user.",
        },
      ],
    });
  }
  if (academyUserGroups && academyUserGroups.length > 0) {
    const userAcademyId = user.academyId;
    const academyId = req.user.academyId._id;
    if (userAcademyId.toString() !== academyId.toString()) {
      throw new AppError("User not authorized to update this user", 403, {
        errors: [
          {
            field: "academyId_token",
            message: "User not authorized to update this user.",
          },
        ],
      });
    }
    const userGroups = await AcademyUserGroup.find({
      _id: { $in: academyUserGroups },
      academyId: userAcademyId,
    });
    if (userGroups.length !== academyUserGroups.length) {
      throw new AppError(
        "One or more user groups not found or don't belong to this academy",
        400,
        {
          errors: [
            {
              field: "academyUserGroups",
              message:
                "Some user group IDs are invalid or do not belong to the specified academy.",
            },
          ],
        }
      );
    }
  }
  const updateData = {
    name,
    email: email ? email.toLowerCase() : undefined,
    academyUserGroups,
  };
  if (password) {
    updateData.password = await bcrypt.hash(password, 12);
  }
  const updatedUser = await AcademyUser.findByIdAndUpdate(id, updateData, {
    new: true,
    runValidators: true,
  })
    .populate("academyId", "name email")
    .populate("academyUserGroups", "name ");

  return new AppSuccess(res, {
    message: "Academy user updated successfully",
    data: updatedUser,
    statusCode: 200,
  });
});

// Delete Academy User
export const deleteAcademyUser = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const user = await AcademyUser.findById(id);
  if (!user) {
    throw new AppError("Academy user not found", 404, {
      errors: [
        {
          field: "id",
          message: "Academy user not found.",
        },
      ],
    });
  }
  const userAcademyId = user.academyId;
  const academyId = req.user.academyId._id;
  if (userAcademyId.toString() !== academyId.toString()) {
    throw new AppError("User not authorized to delete this user", 403, {
      errors: [
        {
          field: "academyId_token",
          message: "User not authorized to delete this user.",
        },
      ],
    });
  }
  if (user.isDefaultAdmin) {
    throw new AppError("Cannot delete the default admin user", 400, {
      errors: [
        {
          field: "isDefaultAdmin",
          message: "Cannot delete the default admin user.",
        },
      ],
    });
  }
  await AcademyUser.findByIdAndDelete(id);
  return new AppSuccess(res, {
    message: "Academy user deleted successfully",
    statusCode: 200,
  });
});
