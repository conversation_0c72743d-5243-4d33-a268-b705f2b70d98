import { Router } from "express";
import {
  createAcademyUser,
  getAllAcademyUsers,
  getAcademyUserById,
  updateAcademyUser,
  deleteAcademyUser,
} from "./academyUserController.js";
import { academyUserProtect } from "../../utils/auth.js";
import { validateRequest } from "../../middlewares/validations.js";
import { academyUserSchema, updateAcademyUserSchema } from "./academyUserValidation.js";
import { checkAcademyUserAccess } from "../utils/auth.js";

const router = Router();
// All routes require academy user authentication
router.use(academyUserProtect());

router.route("/")
.post(
  checkAcademyUserAccess("user", "write"),
  validateRequest(academyUserSchema),
  createAcademyUser
)
.get(
  checkAcademyUserAccess("user", "read"),
  getAllAcademyUsers
);

router.route("/:id")
.get(
  checkAcademyUserAccess("user", "read"),
  getAcademyUserById
)
.patch(
  checkAcademyUserAccess("user", "write"), 
  validateRequest(updateAcademyUserSchema),
  updateAcademyUser
)
.delete(
  checkAcademyUserAccess("user", "delete"),
  deleteAcademyUser
);

export default router;
