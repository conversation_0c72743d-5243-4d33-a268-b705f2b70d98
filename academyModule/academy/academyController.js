import { Academy } from "./academyModel.js";
import validator from "validator";
import { multerUpload } from "../../utils/awsHelper.js";
import { SECRETS } from "../../utils/config.js";
import AWS from "aws-sdk";
import bcrypt from "bcryptjs";
import crypto from "crypto";
import { setAccessTokenCookies, setRefreshTokenCookies } from "../utils/jwt.js";
import { Course } from "../../courses/courseModal.js";
import { Booking } from "../../bookings/bookingModal.js";
import { Events } from "../../events/eventModal.js";
import { sendEmail, sendSms } from "../../utils/msg.js";
import AppSuccess from "../../middlewares/appSuccess.js";
import AppError from "../../middlewares/appError.js";
import { catchAsync } from "../../utils/helpers.js";
import mongoose from "mongoose";
import { createDefaultAcademyAdminUserGroup } from "../academyUserGroup/academyUserGroupController.js";
import { createDefaultAcademyAdminUser } from "../academyUser/academyUserController.js";
import RefreshToken from "./refreshTokenModel.js";
import {
  loginAcademyUserService,
  refreshAcademyUserAccessTokenService,
} from "./academyService.js";
import AcademyUser from "../academyUser/academyUserModal.js";
import { AcademyFacilities } from "../cms/model/academyFacilities.js";
import { Coach } from "../../coaches/coachModal.js";
import { AcademyBlockCms } from "../cms/model/academyBlocksModal.js";
import AcademyUserGroup from "../academyUserGroup/academyUserGroupModal.js";
import { createDefaultAcademyBlocks } from "../cms/academyCmsController.js";

const AWS_ACCESS_KEY_ID = SECRETS.AWS_ACCESS_KEY_ID;
const AWS_SECRET_ACCESS_KEY = SECRETS.AWS_SECRET_ACCESS_KEY;
const AWS_BUCKET_NAME = SECRETS.AWS_BUCKET_NAME;
const AWS_S3_REGION = SECRETS.AWS_S3_REGION;

AWS.config.update({
  accessKeyId: AWS_ACCESS_KEY_ID,
  secretAccessKey: AWS_SECRET_ACCESS_KEY,
  region: AWS_S3_REGION,
});
const s3 = new AWS.S3();

// Helper function to generate stable facility ID
const generateStableFacilityId = (facility) => {
  const facilityString = `${facility.name}-${facility.addressLine1}-${facility.city}-${facility.state}`;
  const hash = crypto.createHash("md5").update(facilityString).digest("hex");
  return `fac_${hash.substring(0, 12)}`;
};

// Register Academy

export const registerAcademy = catchAsync(async (req, res, next) => {
  const session = await mongoose.startSession();
  let responseData;

  try {
    await session.withTransaction(async () => {
      const {
        name,
        mobile,
        email,
        password,
        gstNumber,
        profileImage,
        academyImages,
        officeAddress,
        companyRegistrationNumber,
        linkedFacilities = [],
        bankDetails,
        panNumber,
        panImage,
        aadhaarNumber,
        aadhaarImage,
        sportsCategories,
        platformFee,
      } = req.body;

      const normalizedEmail = email.toLowerCase();
      const isAdmin = req.userType === "Admin";

      // Generate stable facilityIds for facilities
      const facilitiesWithIds = linkedFacilities.map((facility, index) => {
        if (facility.facilityId) {
          return facility;
        } else {
          return {
            ...facility,
            facilityId: generateStableFacilityId(facility),
          };
        }
      });

      const academyData = {
        name,
        mobile,
        email: normalizedEmail,
        gstNumber,
        academyImages,
        officeAddress,
        companyRegistrationNumber,
        linkedFacilities: facilitiesWithIds,
        status: isAdmin ? "active" : "inactive",
        authStatus: isAdmin ? "authorized" : "unauthorized",
        registrationDate: new Date(),
        bankDetails,
        panNumber,
        panImage,
        aadhaarNumber,
        aadhaarImage,
        platformFee: isAdmin ? platformFee : process.env.PLATFORM_FEE,
        sportsCategories,
        profileImage,
      };

      const academy = new Academy(academyData);
      const savedAcademy = await academy.save({ session });

      if (facilitiesWithIds.length > 0) {
        const cmsFacilities = facilitiesWithIds.map((facility, index) => ({
          academy: savedAcademy._id,
          facilityId: facility.facilityId,
          position: index + 1,
          isActive: true,
        }));
        await AcademyFacilities.insertMany(cmsFacilities, { session });
      }

      // Create default blocks if academy is authorized (admin-created)
      if (isAdmin) {
        await createDefaultAcademyBlocks(savedAcademy._id);
      }

      const academyAdminUserGroup = await createDefaultAcademyAdminUserGroup(
        savedAcademy._id
      );
      const savedAcademyAdminUserGroup = await academyAdminUserGroup.save({
        session,
      });

      const academyAdminUser = await createDefaultAcademyAdminUser(
        savedAcademy._id,
        savedAcademyAdminUserGroup._id,
        {
          name,
          email: normalizedEmail,
          password,
        }
      );
      const savedAcademyAdminUser = await academyAdminUser.save({ session });

      responseData = {
        academy: {
          _id: savedAcademy._id,
          name: savedAcademy.name,
          mobile: savedAcademy.mobile,
          email: savedAcademy.email,
          status: savedAcademy.status,
          authStatus: savedAcademy.authStatus,
          registrationDate: savedAcademy.registrationDate,
          ...(isAdmin && { platformFee: savedAcademy.platformFee }),
          sportsCategories: savedAcademy.sportsCategories,
        },
        academyAdminUserGroup: {
          _id: savedAcademyAdminUserGroup._id,
          name: savedAcademyAdminUserGroup.name,
          description: savedAcademyAdminUserGroup.description,
          access_scopes: savedAcademyAdminUserGroup.access_scopes,
        },
        academyAdminUser: {
          _id: savedAcademyAdminUser._id,
          name: savedAcademyAdminUser.name,
          email: savedAcademyAdminUser.email,
          isDefaultAdmin: savedAcademyAdminUser.isDefaultAdmin,
        },
      };
    });

    await session.endSession();

    return new AppSuccess(res, {
      message: "Academy registered successfully",
      data: responseData,
      statusCode: 201,
    });
  } catch (err) {
    await session.endSession();
    return next(err);
  }
});

// Delete Academy

export const deleteAcademy = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const academy = await Academy.findById(id);

  if (!academy) {
    throw new AppError("Academy not found", 404, {
      errors: [
        {
          field: "academy",
          message: "Academy not found",
        },
      ],
    });
  }
  const coachIds = await Coach.find({ academyId: id }).select("_id");
  if (coachIds.length > 0) {
    throw new AppError("Academy has coaches", 400, {
      errors: [
        {
          field: "academy",
          message: "Academy has coaches",
        },
      ],
    });
  }
  const courseIds = await Course.find({ academy_id: id }).select("_id");
  if (courseIds.length > 0) {
    throw new AppError("Academy has courses", 400, {
      errors: [
        {
          field: "academy",
          message: "Academy has courses",
        },
      ],
    });
  }
  await AcademyFacilities.deleteMany({ academy: id.toString() });
  await AcademyBlockCms.deleteMany({ academy: id.toString() });
  await AcademyUser.deleteMany({ academyId: id.toString() });
  await AcademyUserGroup.deleteMany({ academyId: id.toString() });
  await Academy.findByIdAndDelete(id);
  return new AppSuccess(res, {
    message: "Academy deleted successfully",
    data: academy,
    statusCode: 200,
  });
});
// Academy User Login

export const academyUserLogin = catchAsync(async (req, res) => {
  let { email, password } = req.body;
  email = email.toLowerCase();
  const userResponse = await loginAcademyUserService(email, password);
  // Remove tokens from response body
  const { accessToken, refreshToken, ...userDataWithoutTokens } = userResponse;
  // Set cookies
  setAccessTokenCookies(res, accessToken);
  setRefreshTokenCookies(res, refreshToken);
  return new AppSuccess(res, {
    message: "Login successful",
    data: { user: userResponse },
    statusCode: 200,
  });
});

// Refresh Academy User Access Token

export const refreshAcademyUserAccessToken = catchAsync(async (req, res) => {
  // Get refresh token from cookie or body
  const refreshToken = req.refreshToken;
  const user = req.user;
  const { accessToken } = await refreshAcademyUserAccessTokenService(
    refreshToken,
    user,
    res
  );
  return new AppSuccess(res, {
    message: "Access token refreshed successfully",
    data: { accessToken },
  });
});

// Logout

export const logout = catchAsync(async (req, res, next) => {
  const loggedInUser = req.user;
  const result = await RefreshToken.deleteOne({
    academyUserId: loggedInUser.id,
    sessionId: loggedInUser.sessionId,
  });
  const deleted = result.acknowledged;

  res.clearCookie("accessToken", {
    httpOnly: true,
    secure: true,
    sameSite: "none",
    maxAge: 0,
  });
  res.clearCookie("refreshToken", {
    httpOnly: true,
    secure: true,
    sameSite: "none",
    maxAge: 0,
    path: SECRETS.academyUserRefreshTokenPath,
  });
  return new AppSuccess(res, {
    message: "Logged out successfully",
    data: { deleted },
  });
});

// Get Academies

export const getAcademies = catchAsync(async (req, res, next) => {
  const { page = 1, name, status, authStatus } = req.query;
  const limit = 25;
  const skip = (page - 1) * limit;

  const query = {};
  if (name) {
    query.name = { $regex: name, $options: "i" };
  }
  if (status) query.status = status;
  if (authStatus) query.authStatus = authStatus;

  const [totalResults, academies] = await Promise.all([
    Academy.countDocuments(query),
    Academy.find(query).skip(skip).limit(limit).sort({ createdAt: -1 }),
  ]);
  return new AppSuccess(res, {
    message: "Academies fetched successfully",
    data: {
      academies,
      totalResults,
      length: academies.length,
    },
    statusCode: 200,
  });
});

// Change Academy Status
export const changeStatus = catchAsync(async (req, res, next) => {
  if (!req.auth) {
    throw new AppError("Access not allowed", 400, {
      errors: [
        {
          field: "auth",
          message: "Access not allowed",
        },
      ],
    });
  }

  const id = req.params.id;
  if (!id) {
    throw new AppError("id is required", 400, {
      errors: [
        {
          field: "id",
          message: "id is required",
        },
      ],
    });
  }

  const { status } = req.body;
  const academy = await Academy.findById(id);

  if (!academy) {
    throw new AppError("Academy not found", 404, {
      errors: [
        {
          field: "academy",
          message: "Academy not found",
        },
      ],
    });
  }

  // Check if authStatus is 'authorized'
  if (academy.authStatus !== "authorized") {
    throw new AppError("Admin is not authorized to change status", 400, {
      errors: [
        {
          field: "authStatus",
          message: `Cannot change status. Current authStatus is '${academy.authStatus}'`,
        },
      ],
    });
  }

  // Proceed with status update
  const updatedAcademy = await Academy.findByIdAndUpdate(
    id,
    { status },
    { new: true }
  );

  await Coach.updateMany({ academyId: id }, { status });
  await Course.updateMany({ academy_id: id }, { coachStatus: status });

  return new AppSuccess(res, {
    message: "Status updated successfully",
    data: updatedAcademy,
    statusCode: 200,
  });
});

// Change Academy Authorization Status
export const changeAuthStatus = catchAsync(async (req, res, next) => {
  if (!req.auth) {
    throw new AppError("Access not allowed", 400, {
      errors: [
        {
          field: "auth",
          message: "Access not allowed",
        },
      ],
    });
  }

  const id = req.params.id;
  if (!id) {
    throw new AppError("id is required", 400, {
      errors: [
        {
          field: "id",
          message: "id is required",
        },
      ],
    });
  }

  const { authStatus } = req.body;

  const data = await Academy.findByIdAndUpdate(
    id,
    {
      authStatus,
      approvalDate: new Date(),
      status: "active",
    },
    {
      new: true,
    }
  );

  if (!data) {
    throw new AppError("Academy not found", 404, {
      errors: [
        {
          field: "academy",
          message: "Academy not found",
        },
      ],
    });
  }

  await createDefaultAcademyBlocks(id);

  return new AppSuccess(res, {
    message: "Auth status updated successfully",
    data,
    statusCode: 200,
  });
});

// Get Academy Profile
export const getAcademyById = catchAsync(async (req, res, next) => {
  const id = req.params.id;
  if (!id) {
    throw new AppError("id is required", 400, {
      errors: [
        {
          field: "id",
          message: "id is required",
        },
      ],
    });
  }
  const data = await Academy.findById(id);
  if (!data) {
    throw new AppError("Academy doesn't exist", 404, {
      errors: [
        {
          field: "academy",
          message: "Academy doesn't exist",
        },
      ],
    });
  }
  let responseData = data.toObject();
  if (req.userType !== "Admin") {
    delete responseData.platformFee;
  }
  return new AppSuccess(res, {
    message: "Academy fetched successfully",
    data: responseData,
    statusCode: 200,
  });
});

// Update Academy Profile
export const updateAcademyProfile = catchAsync(async (req, res, next) => {
  const academyId = req.academyId || req.params.id;
  const {
    name,
    mobile,
    gstNumber,
    academyImages,
    officeAddress,
    companyRegistrationNumber,
    linkedFacilities, // Remove default value here
    status,
    authStatus,
    registrationDate,
    bankDetails,
    panNumber,
    panImage,
    aadhaarNumber,
    aadhaarImage,
    sportsCategories,
    platformFee,
    profileImage,
  } = req.body;

  let updateFields = {
    name,
    mobile,
    gstNumber,
    academyImages,
    officeAddress,
    companyRegistrationNumber,
    status,
    authStatus,
    registrationDate,
    bankDetails,
    profileImage,
    panNumber,
    panImage,
    aadhaarNumber,
    aadhaarImage,
    sportsCategories,
  };

  // Only include linkedFacilities in updateFields if it's provided in the request
  if (linkedFacilities !== undefined) {
    updateFields.linkedFacilities = linkedFacilities;
  }

  if (platformFee) {
    if (req.userType === "Admin") {
      updateFields.platformFee = platformFee;
    } else {
      return next(
        new AppError("Unauthorized to update platform fee", 403, {
          errors: [
            {
              field: "platformFee",
              message: "Only Admin can update platform fee",
            },
          ],
        })
      );
    }
  }

  // Only process facilities if linkedFacilities is provided
  let facilitiesWithIds = [];
  if (linkedFacilities !== undefined) {
    // Generate stable facilityIds for facilities
    facilitiesWithIds = linkedFacilities.map((facility, index) => {
      if (facility.facilityId) {
        return facility;
      } else {
        return {
          ...facility,
          facilityId: generateStableFacilityId(facility),
        };
      }
    });

    // Update academy with facilities that have stable IDs
    updateFields.linkedFacilities = facilitiesWithIds;
  }

  const academy = await Academy.findByIdAndUpdate(academyId, updateFields, {
    new: true,
    runValidators: true,
  });

  if (!academy) {
    throw new AppError("Academy not found", 404, {
      errors: [
        {
          field: "academy",
          message: "Academy not found",
        },
      ],
    });
  }

  // Only update AcademyFacilities if linkedFacilities was provided
  if (linkedFacilities !== undefined) {
    // Get existing facilities and their states
    const existingFacilities = await AcademyFacilities.find({
      academy: academyId,
    });

    const existingFacilityMap = new Map();
    existingFacilities.forEach((fac) => {
      existingFacilityMap.set(fac.facilityId, fac.isActive);
    });

    const newFacilityIds = facilitiesWithIds.map((f) => f.facilityId);

    // Remove facilities not in the new list
    await AcademyFacilities.deleteMany({
      academy: academyId,
      facilityId: { $nin: newFacilityIds },
    });

    // Upsert facilities using stable facilityId
    const facilitiesToUpsert = facilitiesWithIds.map((facility, index) => {
      const isExisting = existingFacilityMap.has(facility.facilityId);

      return {
        updateOne: {
          filter: {
            academy: academyId,
            facilityId: facility.facilityId,
          },
          update: {
            $set: {
              position: index + 1,
              isActive: isExisting
                ? existingFacilityMap.get(facility.facilityId)
                : true,
            },
          },
          upsert: true,
        },
      };
    });

    if (facilitiesToUpsert.length > 0) {
      await AcademyFacilities.bulkWrite(facilitiesToUpsert);
    }
  }

  return new AppSuccess(res, {
    message: "Academy profile updated successfully",
    data: academy,
    statusCode: 200,
  });
});

// Upload Academy Image

export const uploadFile = async (req, res) => {
  try {
    const uploadSingle = multerUpload(
      AWS_BUCKET_NAME,
      "images2/academy"
    ).single("image");

    uploadSingle(req, res, async (err) => {
      if (err) {
        return res.status(400).json({ error: err.message });
      }
      const url = req.body.url;
      if (url) {
        const deleteParams = {
          Bucket: AWS_BUCKET_NAME,
          Key: `images2/academy/${url.split("/").pop()}`,
        };
        const deleteResult = await s3.deleteObject(deleteParams).promise();
      }
      // Assuming your uploaded file is public
      // const publicUrl = `${AWS_S3_BASE_URL}/${AWS_BUCKET_NAME}/${req?.file?.key}`;

      return res.status(200).json({ url: req?.file?.location });
    });
  } catch (error) {
    res.status(500).json({
      error: error.message || "Internal server error",
    });
  }
};

export const requestPasswordReset = catchAsync(async (req, res, next) => {
  const { email } = req.params;
  const academyUser = await AcademyUser.findOne({ email: email.toLowerCase() });

  if (!academyUser) {
    throw new AppError("Academy not found", 404, {
      errors: [
        {
          field: "email",
          message: "Academy not found",
        },
      ],
    });
  }

  const token = crypto.randomBytes(32).toString("hex");
  const tokenExpiry = Date.now() + 3600000; // Token valid for 1 hour

  academyUser.resetPasswordToken = token;
  academyUser.resetPasswordExpires = tokenExpiry;
  await academyUser.save();

  await sendEmail(
    academyUser.name,
    email,
    "reset_password_14",
    `{"Name": "${academyUser.name}", "Reset_Password_Link":"${SECRETS.academyBaseUrl}/resetPassword?token=${token}"}`
  );

  return new AppSuccess(res, {
    message: "Reset password link sent successfully",
    statusCode: 200,
    data: {
      name: academyUser.name,
      email: academyUser.email,
      resetLink: `${SECRETS.academyBaseUrl}/resetPassword?token=${token}`,
      expiresAt: new Date(tokenExpiry).toISOString(),
    },
  });
});

export const resetpassword = catchAsync(async (req, res, next) => {
  const { token, newPassword } = req.body;
  const academy = await AcademyUser.findOne({
    resetPasswordToken: token,
    resetPasswordExpires: { $gt: Date.now() },
  });

  if (!academy) {
    throw new AppError("Invalid or expired token", 400, {
      errors: [
        {
          field: "token",
          message: "Invalid or expired token",
        },
      ],
    });
  }
  const salt = newPassword ? await bcrypt.genSalt(10) : undefined;
  const secPass = newPassword
    ? await bcrypt.hash(newPassword, salt)
    : undefined;

  academy.password = secPass;
  academy.resetPasswordToken = undefined;
  academy.resetPasswordExpires = undefined;
  await academy.save();

  return new AppSuccess(res, {
    message: "Password reset successfully",
    statusCode: 200,
  });
});

export const academyDashboardLogs = catchAsync(async (req, res, next) => {
  if (!req.auth) {
    throw new AppError("Access not allowed", 400, {
      errors: [
        {
          field: "auth",
          message: "Access not allowed",
        },
      ],
    });
  }

  const { date, coachId } = req.body;
  const id = req.user.academyId._id;
  if (!id) {
    throw new AppError("Id is required in token", 400, {
      errors: [
        {
          field: "id",
          message: "Id is required in token",
        },
      ],
    });
  }

  const startDate = new Date(date);
  const endDate = new Date(date);
  endDate.setDate(endDate.getDate() + 1);
  const startDate1 = `${date}T00:00:00.000Z`;
  const endDate1 = `${date}T23:59:59.000Z`;

  // Build course query
  let courseQuery = {
    academy_id: id,
    "dates.dates": date,
  };

  if (coachId) {
    courseQuery.coach_id = coachId;
  }

  // Build events query
  let eventsQuery = {
    dates: date,
  };

  if (coachId) {
    eventsQuery.coachId = coachId;
  }

  // Get current time for comparison
  const currentTime = new Date();
  const currentTimeString = currentTime.toTimeString().slice(0, 5); // Format: HH:mm

  // Fetch all courses first
  let coursesAndClasses = await Course.find(courseQuery);

  // Always filter to get only 4 courses closest to current time
  if (coursesAndClasses.length > 4) {
    // Sort courses by how close their start time is to current time
    coursesAndClasses.sort((a, b) => {
      const timeA = new Date(`${date}T${a.dates.startTime}`);
      const timeB = new Date(`${date}T${b.dates.startTime}`);
      const currentTimeDate = new Date(`${date}T${currentTimeString}`);

      const diffA = Math.abs(timeA - currentTimeDate);
      const diffB = Math.abs(timeB - currentTimeDate);

      return diffA - diffB;
    });

    // Take only the first 4 courses
    coursesAndClasses = coursesAndClasses.slice(0, 4);
  }

  const courseIds = coursesAndClasses.map((item) => item._id);
  const courseBookings = await Booking.find({
    courseId: { $in: courseIds },
    "classes.date": { $gte: startDate1, $lt: endDate1 },
  });
  const events = await Events.find(eventsQuery);

  let bookingClass = [];
  courseBookings.forEach((booking) => {
    booking.classes.forEach((cls) => {
      bookingClass.push({
        bookingId: booking._id,
        booking: booking.bookingId,
        orderId: booking.orderId,
        coachId: booking.coachId,
        courseId: booking.courseId,
        courseName: booking.courseName,
        player: booking.player,
        playerName: booking.playerName,
        playerEmail: booking.playerEmail,
        courseType: booking.courseType,
        classId: cls._id,
        classDate: cls.date,
        classStartTime: cls.startTime,
        classEndTime: cls.endTime,
        duration: cls.duration,
        classStatus: cls.status,
        classType: cls.type,
        classDescription: cls.description,
        classLocation: cls.location,
        classPrice: cls.price,
        classDiscount: cls.discount,
        classAttendance: cls.attendance,
        classCoachAttendance: cls.coachAttendance,
      });
    });
  });

  let finalData = [];

  // Add events to final data
  events.forEach((event) => {
    finalData.push({
      type: "event",
      name: event.name,
      startTime: event.startTime,
      endTime: event.endTime,
      facility: "",
      coursePrice: "",
      playerEnrolled: "",
      maxGroupSize: "",
      image: "",
      bookings: [],
    });
  });

  // Add courses to final data
  coursesAndClasses.forEach((item) => {
    const itemBookings = bookingClass.filter((booking) => {
      return (
        booking.courseId.equals(item._id) &&
        booking.classDate >= startDate &&
        booking.classDate < endDate
      );
    });

    finalData.push({
      type: item.classType,
      name: item.courseName,
      startTime: item.dates.startTime,
      endTime: item.dates.endTime,
      facility: item.facility.name,
      coursePrice: item.fees.fees,
      playerEnrolled: item.playerEnrolled,
      maxGroupSize: item.maxGroupSize,
      image: item.images[0]?.url || "",
      id: item._id,
      bookings: itemBookings.map((booking) => ({
        id: booking.bookingId,
        name: booking.playerName,
        playerId: booking.player,
        startTime: booking.classStartTime,
        endTime: booking.classEndTime,
        pricePaid: booking.fees,
        classId: booking.classId,
        attendance: booking.attendance || "NA",
        coachAttendance: booking.coachAttendance || "NA",
        status: booking.status || "upcoming",
      })),
    });
  });

  // Sort final data by start time
  finalData.sort((a, b) => {
    const startTimeA = new Date(`${date}T${a.startTime}`);
    const startTimeB = new Date(`${date}T${b.startTime}`);
    return startTimeA - startTimeB;
  });

  // Sort bookings within each item by start time
  finalData.forEach((item) => {
    item.bookings.sort((a, b) => {
      const startTimeA = new Date(`${date}T${a.startTime}`);
      const startTimeB = new Date(`${date}T${b.startTime}`);
      return startTimeA - startTimeB;
    });
  });
  return new AppSuccess(res, {
    message: "Dashboard logs fetched successfully",
    data: finalData,
    statusCode: 200,
  });
});
