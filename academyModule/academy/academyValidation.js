import Joi from "joi";

export const academyLoginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
});

export const academyRegistrationSchema = Joi.object({
  name: Joi.string().required(),
  mobile: Joi.string().required(),
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
  gstNumber: Joi.string().optional(),
  sportsCategories: Joi.array().items(Joi.string()).optional(),
  academyImages: Joi.array().items(Joi.string()).optional(),
  platformFee: Joi.number().optional(),
  officeAddress: Joi.object({
    addressLine1: Joi.string().optional(),
    addressLine2: Joi.string().optional(),
    city: Joi.string().optional(),
    state: Joi.string().optional(),
    pinCode: Joi.string().optional(),
    country: Joi.string().optional(),
  }).optional(),
  companyRegistrationNumber: Joi.string().optional(),
  linkedFacilities: Joi.array()
    .items(
      Joi.object({
        facilityId: Joi.string().optional(),
        name: Joi.string().optional(),
        addressLine1: Joi.string().optional(),
        addressLine2: Joi.string().optional(),
        city: Joi.string().optional(),
        state: Joi.string().optional(),
        pinCode: Joi.string().optional(),
        country: Joi.string().optional(),
        amenities: Joi.string().optional(),
        location: Joi.object({
          type: Joi.string().valid("Point").default("Point"),
          coordinates: Joi.array().items(Joi.number()).optional(),
          is_location_exact: Joi.boolean().default(true),
        }).optional(),
      })
    )
    .optional(),
  bankDetails: Joi.object({
    accountNumber: Joi.string().optional(),
    accountHolderName: Joi.string().optional(),
    ifsc: Joi.string().optional(),
  }).optional(),
  panNumber: Joi.string().required(),
  panImage: Joi.array().items(Joi.string()).required().allow(null),
  aadhaarNumber: Joi.string().required(),
  profileImage: Joi.string().required().allow(null),
  aadhaarImage: Joi.array().items(Joi.string()).required().allow(null),
  current_user: Joi.optional(),
});

export const changeStatusSchema = Joi.object({
  status: Joi.string().valid("active", "inactive", "deactivated").required(),
  current_user: Joi.optional(),
});

export const changeAuthStatusSchema = Joi.object({
  authStatus: Joi.string().valid("authorized", "unauthorized").required(),
  current_user: Joi.optional(),
});

export const requestPasswordResetSchema = Joi.object({
  email: Joi.string().email().required(),
});

export const resetPasswordSchema = Joi.object({
  token: Joi.string().required(),
  newPassword: Joi.string().min(6).required(),
});

export const updateAcademyProfileSchema = Joi.object({
  name: Joi.string().optional(),
  email: Joi.string().email().optional(),
  mobile: Joi.string().optional(),
  current_user: Joi.optional(),
  gstNumber: Joi.string().optional(),
  profileImage: Joi.string().optional().allow(null),
  aadhaarImage: Joi.array().items(Joi.string()).optional().allow(null),
  panImage: Joi.array().items(Joi.string()).optional().allow(null),
  aadhaarNumber: Joi.string().optional(),
  panNumber: Joi.string().optional(),
  companyRegistrationNumber: Joi.string().optional(),
  academyImages: Joi.array().items(Joi.string()).optional(),
  officeAddress: Joi.object({
    addressLine1: Joi.string().optional(),
    addressLine2: Joi.string().optional(),
    city: Joi.string().optional(),
    state: Joi.string().optional(),
    pinCode: Joi.string().optional(),
    country: Joi.string().optional(),
  }).optional(),
  sportsCategories: Joi.array().items(Joi.string()).optional(),
  linkedFacilities: Joi.array()
    .items(
      Joi.object({
        facilityId: Joi.string().optional(),
        name: Joi.string().optional(),
        addressLine1: Joi.string().optional(),
        addressLine2: Joi.string().optional(),
        city: Joi.string().optional(),
        state: Joi.string().optional(),
        pinCode: Joi.string().optional(),
        country: Joi.string().optional(),
        amenities: Joi.string().optional(),
        location: Joi.object({
          type: Joi.string().valid("Point").default("Point"),
          coordinates: Joi.array().items(Joi.number()).optional(),
          is_location_exact: Joi.boolean().default(true),
        }).optional(),
      })
    )
    .optional(),
  bankDetails: Joi.object({
    accountNumber: Joi.string().optional(),
    accountHolderName: Joi.string().optional(),
    ifsc: Joi.string().optional(),
  }).optional(),
  platformFee: Joi.number().optional(),
});

export const academyDashboardLogsSchema = Joi.object({
  date: Joi.string().required(),
  coachId: Joi.string().optional(),
});
