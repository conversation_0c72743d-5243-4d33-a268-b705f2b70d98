import AppError from "../../middlewares/appError.js";
import { SECRETS } from "../../utils/config.js";
import { generateAccessToken, generateRefreshToken, setAccessTokenCookies } from "../utils/jwt.js";
import { calculateRefreshExpiresAt, comparePassword, generateSessionId } from "../utils/helper.js";
import AcademyUser from "../academyUser/academyUserModal.js";
import RefreshToken from "./refreshTokenModel.js";
import { getUserGroupAccessScopes } from "../academyUserGroup/academyUserGroupController.js";

// Authenticate academy user
export const loginAcademyUserService = async (email, password) => {
  // Find user with populated user groups
  const user = await AcademyUser.findOne({  email: email.toLowerCase() })
    .populate("academyId", "name email status authStatus")
    .populate("academyUserGroups", "name access_scopes");

  if (!user) {
    throw new AppError("Invalid email or password", 401, {
      errors: [
        {
          field: "email",
          message: "No user found with this email",
        },
      ],
    });
  }

  // Verify password
  const isPasswordValid = await comparePassword(password, user.password);
  if (!isPasswordValid) {
    throw new AppError("Invalid email or password", 401, {
      errors: [
        {
          field: "password",
          message: "Incorrect password",
        },
      ],
    });
  }

  // Get merged access scopes
  const accessScopes = await getUserGroupAccessScopes(
    user.academyUserGroups.map((group) => group._id)
  );

  // Generate sessionId
  const sessionId = generateSessionId();

  const detailedUser = {
    id: user._id.toString(),
    name: user.name,
    email: user.email,
    academyId: user.academyId,
    academyUserGroups: user.academyUserGroups,
    accessScopes,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
    sessionId,
    userType: "academy",
  };

  // Generate tokens
  const accessTokenDuration = SECRETS.jwtExp; // Access token expiration time
  const accessToken = generateAccessToken(detailedUser, accessTokenDuration);
  const refreshTokenDuration = SECRETS.jwtRefreshExp; // Refresh token expiration time
  const refreshToken = generateRefreshToken(
    user._id.toString(),
    refreshTokenDuration
  );

  // Dynamically calculate expiration time
  const expiresAt = calculateRefreshExpiresAt(refreshTokenDuration);

  // Save refresh token without replacing existing ones
  await RefreshToken.create({
    academyUserId: user._id,
    sessionId,
    token: refreshToken,
    expiresAt,
  });

  // Return user data without password
  const userResponse = {
    user: detailedUser,
    accessToken,
    refreshToken,
  };

  return userResponse;
};

// Verify and refresh access token using refresh token
export const refreshAcademyUserAccessTokenService = async (
  refreshToken,
  user,
  res
) => {
  if (!refreshToken) {
    throw new AppError("Refresh token is missing", 400, {
      errors: [
        {
          field: "refreshToken",
          message: "Refresh token is missing",
        },
      ],
    });
  }

  // Find refresh token in database
  const tokenRecord = await RefreshToken.findOne({
    token: refreshToken,
    academyUserId: user.id,
  });

  if (!tokenRecord) {
    throw new AppError("Invalid refresh token", 401, {
      errors: [
        {
          field: "refreshToken",
          message: "Invalid refresh token",
        },
      ],
    });
  }

  const academyUser = await AcademyUser.findById(tokenRecord.academyUserId)
    .populate("academyId", "name email status authStatus")
    .populate("academyUserGroups", "name access_scopes");

  if (!academyUser) {
    throw new AppError("Academy user not found", 401, {
      errors: [
        {
          field: "academyUser",
          message: "Academy user not found",
        },
      ],
    });
  }

  const accessScopes = await getUserGroupAccessScopes(
    academyUser.academyUserGroups.map((group) => group._id)
  );

  const sessionId = generateSessionId();

  const detailedUser = {
    id: academyUser._id.toString(),
    name: academyUser.name,
    email: academyUser.email,
    academyId: academyUser.academyId,
    academyUserGroups: academyUser.academyUserGroups,
    accessScopes,
    createdAt: academyUser.createdAt,
    updatedAt: academyUser.updatedAt,
    sessionId,
  };

  const accessTokenDuration = SECRETS.jwtExp; // Access token expiration time
  const newAccessToken = generateAccessToken(detailedUser, accessTokenDuration);

  setAccessTokenCookies(res, newAccessToken);

  return {
    accessToken: newAccessToken,
  };
};
