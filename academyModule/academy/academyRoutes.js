import { Router } from "express";
import {
  registerAcademy,
  academyUserLogin,
  refreshAcademyUserAccessToken,
  logout,
  updateAcademyProfile,
  getAcademies,
  changeStatus,
  changeAuthStatus,
  getAcademyById,
  requestPasswordReset,
  resetpassword,
  academyDashboardLogs,
  deleteAcademy,
} from "./academyController.js";
import {
  academyAccessControl,
  checkAcademyUserAccess,
  verifyToken,
} from "../utils/auth.js";
import authenticateUserAccess from "../../Helpers/authenticationUser.js";
import {
  academyLoginSchema,
  academyRegistrationSchema,
  changeStatusSchema,
  changeAuthStatusSchema,
  updateAcademyProfileSchema,
  academyDashboardLogsSchema,
} from "./academyValidation.js";
import { validateRequest } from "../../middlewares/validations.js";
import { downloadFile } from "../../utils/awsHelper.js";
import { uploadFile } from "./academyController.js";
import { academyUserProtect } from "../../utils/auth.js";

const router = Router();

router
  .route("/login")
  .post(validateRequest(academyLoginSchema), academyUserLogin);

// Refresh token route (no authentication required)
router.put(
  "/refresh-token",
  verifyToken("refresh"),
  refreshAcademyUserAccessToken
);

router.delete("/logout", verifyToken("access"), logout);

router
  .route("/")
  .post(
    authenticateUserAccess("academy", "write"),
    validateRequest(academyRegistrationSchema),
    registerAcademy
  )
  .get(authenticateUserAccess("academy", "read"), getAcademies);

router
  .route("/updateStatus/:id")
  .patch(
    authenticateUserAccess("academy", "write"),
    validateRequest(changeStatusSchema),
    changeStatus
  );
router
  .route("/updateAuthStatus/:id")
  .patch(
    authenticateUserAccess("academy", "write"),
    validateRequest(changeAuthStatusSchema),
    changeAuthStatus
  );

router
  .route("/:id")
  .get(
    authenticateUserAccess("academy", "read"),
    academyUserProtect(true),
    academyAccessControl,
    getAcademyById
  )
  .patch(
    academyUserProtect(),
    authenticateUserAccess("academy", "write"),
    academyAccessControl,
    validateRequest(updateAcademyProfileSchema),
    updateAcademyProfile
  )
  .delete(academyUserProtect(), academyAccessControl, deleteAcademy);

router.route("/uploadImage").post(uploadFile);
router.route("/download").post(downloadFile);
router
  .route("/dashboard")
  .post(
    academyUserProtect(),
    validateRequest(academyDashboardLogsSchema),
    academyDashboardLogs
  );
router.route("/requestResetPassword/:email").post(requestPasswordReset);
router.route("/resetPassword/").post(resetpassword);

export default router;
