import mongoose from "mongoose";
const { Schema, model } = mongoose;

const academyTopCourseSchema = new Schema(
  {
    academy: { type: Schema.Types.ObjectId, ref: "academy" },
    course: { type: Schema.Types.ObjectId, ref: "course" },
    position: { type: Number },
  },
  { timestamps: true }
);

// Pre-save middleware to automatically assign position
academyTopCourseSchema.pre("save", async function (next) {
  try {
    if (!this.isNew || typeof this.position !== "undefined") {
      return next(); // If not new or position already defined, skip
    }
    const maxPositionDoc = await this.constructor.findOne(
      { academy: this.academy }, // Scope to this academy
      {},
      { sort: { position: -1 } }
    ); // Find the document with maximum position
    const maxPosition = maxPositionDoc ? maxPositionDoc.position : 0; // Get the maximum position
    this.position = maxPosition + 1; // Assign the next position
    next();
  } catch (error) {
    next(error);
  }
});

// Create compound index to prevent duplicate academy-course combinations
academyTopCourseSchema.index({ academy: 1, course: 1 }, { unique: true });

export const AcademyTopCourse = model(
  "academyTopCourseCms",
  academyTopCourseSchema
);
