import mongoose from "mongoose";
const { Schema, model } = mongoose;

const academyBlockSchema = new Schema(
  {
    academy: { type: Schema.Types.ObjectId, ref: "academy" },
    identity: { type: String, required: true },
    visibility: { type: Boolean, required: true, default: false },
    title: { type: String, required: true },
    position: { type: Number, required: true },
    max: { type: Number },
    collectionName: { type: String },
  },
  { timestamps: true }
);

export const AcademyBlockCms = model("academyBlockCms", academyBlockSchema);
