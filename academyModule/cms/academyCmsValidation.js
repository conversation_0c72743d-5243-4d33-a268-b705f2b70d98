import Joi from "joi";

export const addTopCoachSchema = Joi.object({
  documents: Joi.array()
    .items(
      Joi.object({
        coach: Joi.string().required(),
        position: Joi.number().optional(),
      })
    )
    .max(15)
    .required(),
});

export const updateCoachPositionSchema = Joi.object({
  updates: Joi.array()
    .items(
      Joi.object({
        id: Joi.string().required(),
        newPosition: Joi.number().required(),
      })
    )
    .required(),
});

// ==================== COURSE VALIDATION SCHEMAS ====================

export const addTopCourseSchema = Joi.object({
  documents: Joi.array()
    .items(
      Joi.object({
        course: Joi.string().required(),
        position: Joi.number().optional(),
      })
    )
    .max(15)
    .required(),
});

export const updateCoursePositionSchema = Joi.object({
  updates: Joi.array()
    .items(
      Joi.object({
        id: Joi.string().required(),
        newPosition: Joi.number().required(),
      })
    )
    .required(),
});

// ==================== ACADEMY DESCRIPTION VALIDATION SCHEMA ====================

const validateHtmlContent = (value, helpers) => {
  // Remove HTML tags and decode HTML entities
  const textContent = value
    .replace(/<[^>]*>/g, "") // Remove HTML tags
    .replace(/&nbsp;/g, " ") // Replace &nbsp; with space
    .replace(/&amp;/g, "&") // Replace &amp; with &
    .replace(/&lt;/g, "<") // Replace &lt; with <
    .replace(/&gt;/g, ">") // Replace &gt; with >
    .replace(/&quot;/g, '"') // Replace &quot; with "
    .trim();

  if (textContent.length < 10) {
    return helpers.error("string.htmlContentTooShort");
  }

  // Optional: Check for valid HTML structure
  const openTags = (value.match(/<[^/][^>]*>/g) || []).length;
  const closeTags = (value.match(/<\/[^>]*>/g) || []).length;

  if (openTags !== closeTags) {
    return helpers.error("string.invalidHtml");
  }

  return value;
};

export const academyDescriptionSchema = Joi.object({
  description: Joi.string()
    .trim()
    .min(10)
    .max(5000)
    .required()
    .custom(validateHtmlContent)
    .messages({
      "string.min": "Description must be at least 10 characters long",
      "string.max": "Description cannot exceed 5000 characters",
      "any.required": "Description is required",
      "string.htmlContentTooShort":
        "Description must contain at least 10 characters of actual text content",
      "string.invalidHtml": "Description contains invalid HTML structure",
    }),
});

// ==================== ACADEMY TESTIMONIALS VALIDATION SCHEMA ====================

export const addAcademyTestimonialsSchema = Joi.object({
  name: Joi.string().trim().min(1).required().messages({
    "string.min": "Name cannot be empty",
    "any.required": "Name is required",
  }),
  description: Joi.string().trim().min(10).max(5000).required().messages({
    "string.min": "Description must be at least 10 characters long",
    "string.max": "Description cannot exceed 5000 characters",
    "any.required": "Description is required",
  }),
});

export const updateAcademyTestimonialsSchema = Joi.object({
  name: Joi.string().trim().min(1).required().messages({
    "string.min": "Name cannot be empty",
    "any.required": "Name is required",
  }),
  description: Joi.string().trim().min(10).max(5000).required().messages({
    "string.min": "Description must be at least 10 characters long",
    "string.max": "Description cannot exceed 5000 characters",
    "any.required": "Description is required",
  }),
});

export const updateTestimonialPositionSchema = Joi.object({
  updates: Joi.array()
    .items(
      Joi.object({
        id: Joi.string().required(),
        newPosition: Joi.number().required(),
      })
    )
    .required(),
});

export const writeAcademyBlockSchema = Joi.object({
  identity: Joi.string().trim().min(1).required().messages({
    "string.min": "Identity cannot be empty",
    "any.required": "Identity is required",
  }),
  visibility: Joi.boolean().required(),
  title: Joi.string().trim().min(1).required().messages({
    "string.min": "Title cannot be empty",
    "any.required": "Title is required",
  }),
  position: Joi.number().required(),
  max: Joi.number().required(),
  collectionName: Joi.string()
    .trim()
    .min(1)
    .required()
    .valid(
      "academyTopCoachCms",
      "academyTopCourseCms",
      "academyTestimonial",
      "academyDescription",
      "academyFacilities"
    )
    .messages({
      "string.min": "Collection name cannot be empty",
      "any.required": "Collection name is required",
      "string.valid": "Invalid collection name",
    }),
});