import mongoose from "mongoose";
const { Schema, model } = mongoose;

const blockSchema = new Schema(
  {
    identity: { type: String, require: true },
    visibility: {type: Boolean, require: true, default: true},
    title: {type: String, require: true},
    position: {type: Number, require: true},
    max: {type: Number},
    collectionName: { type: String }
  },
  { timestamps: true }
);

export const BlockCms = model("blockCms", blockSchema);
