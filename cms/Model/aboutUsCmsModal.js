import mongoose from "mongoose";
const { Schema, model, SchemaType } = mongoose;
const AboutUsCms = new Schema(
  {
    aboutUsData: {
      type: String,
    },
    founderDetails: [
      {
        description: {
          type: String,
        },
        image: {
          type: String,
        },
      },
    ],
  },
  { timestamps: true }
);

export const CmsAboutUs = model("aboutUsCms", AboutUsCms);
