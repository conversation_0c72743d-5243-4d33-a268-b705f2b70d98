import mongoose from "mongoose";
const { Schema, model } = mongoose;

const TopCourseSchema = new Schema(
  {
    course: { type: Schema.Types.ObjectId, ref: "course" },
    position: {type: Number}
  },
  { timestamps: true }
);

// Pre-save middleware to automatically assign position
TopCourseSchema.pre('save', async function (next) {
  try {
    if (!this.isNew || typeof this.position !== 'undefined') {
      return next(); // If not new or position already defined, skip
    }
    const maxPositionDoc = await this.constructor.findOne({}, {}, { sort: { position: -1 } }); // Find the document with maximum position
    const maxPosition = maxPositionDoc ? maxPositionDoc.position : 0; // Get the maximum position
    this.position = maxPosition + 1; // Assign the next position
    next();
  } catch (error) {
    next(error);
  }
});

export const TopCourse = model("topCourseCms", TopCourseSchema);
