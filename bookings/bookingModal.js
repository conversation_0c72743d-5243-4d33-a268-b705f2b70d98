import mongoose from "mongoose";
const { Schema, model } = mongoose;

const bookingSchema = new Schema(
  {
    bookingId: {
      type: String,
      required: true,
      unique: true,
    },
    orderId: {
      type: String,
    },
    academyId: {
      type: Schema.Types.ObjectId,
      ref: "academy",
    },
    coachId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "coach",
    },
    courseId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "course",
    },
    courseName: {
      type: String,
      required: true,
    },
    player: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "player",
    },
    playerName: {
      type: String,
      required: true,
    },
    playerEmail: {
      type: String,
      required: true,
    },
    courseType: {
      type: String,
      required: true,
      enum: ["class", "course"],
    },
    classes: [
      {
        date: {
          type: Date,
          required: true,
        },
        startTime: {
          type: String,
          required: true,
        },
        endTime: {
          type: String,
          required: true,
        },
        duration: {
          type: String,
          required: true,
        },
        days: { type: String, required: true },
        fees: {
          type: Number,
          required: true,
        },
        status: {
          type: String,
          default: "upcoming",
          enum: ["upcoming", "completed", "cancelled", "rescheduled"],
        },
        cancellationDate: {
          type: Date,
        },
        eventId: {
          type: String,
        },
        otp:{
          type: String,
          required: false,
        },
        attendance: {
          type: String,
          default: "NA",
          enum: ["present", "absent", "NA"],
        },
        coachAttendance: {
          type: String,
          default: "NA",
          enum: ["present", "absent", "NA"],
        },
        paymentStatus: {
          type: String,
          default: "unpaid",
          enum: ["paid", "unpaid"],
        },
        invoice: {
          type: String,
          unique: true,
        },
        coachFeesAfterCancelation: {
          type: Number,
          required: true,
        },
      },
    ],
    groupSize: {
      type: Number,
      required: true,
    },
    pricePaid: {
      type: Number,
      required: true,
    },
    razorPayPaymentId: {
      type: String,
    },
    paymentMode: {
      type: String,
    },
    paymentId: {
      type: String,
    },
    status: {
      type: String,
      default: "Active",
      enum: ["Active", "Inactive"],
    },
    bookingDate: {
      type: Date,
      default: new Date(),
    },
    paymentStatus: {
      type: String,
      default: "Pending",
      enum: ["Pending", "Success", "Rejected"],
    },
    wallet: {
      type: Boolean,
      required: true,
    },
    walletAmount: {
      type: Number,
      required: true,
    },
    rating: {
      type: Boolean,
      default: false,
    },
    stars: {
      type: Number,
      default: 0,
    },
  },
  { timestamps: true }
);

bookingSchema.index({ bookingId: 1 }, { unique: true, sparse: true });

export const Booking = model("bookings", bookingSchema);
