import { Router } from "express";

import {
  createCourse,
  getCourses,
  getCourseById,
  updateCourse,
  deleteCourse,
  uploadFile,
  getCourseByCoachId,
  getAvailableSlots,
  giveRating,
} from "./courseController.js";
import { getSearchResults } from "./courseFilter.js";
import { coachProtect, flexibleAuth, playerProtect } from "../utils/auth.js";

const router = Router();

router
  .route("/")
  .get(flexibleAuth("course", "read", true), getCourses)
  .post(flexibleAuth("course", "write"), coachProtect, createCourse);
router.route("/filter").get(getSearchResults);
router.route("/coach/:id").get(getCourseByCoachId);
router
  .route("/:id")
  .get(getCourseById)
  .patch(flexibleAuth("course", "write"), coachProtect, updateCourse)
  .delete(flexibleAuth("course", "delete"), coachProtect, deleteCourse);
router.route("/uploadImage/").post(uploadFile);
router
  .route("/availableSlots/:id")
  .post(flexibleAuth("course", "read"), coachProtect, getAvailableSlots);
router.route("/ratings/:id").post(playerProtect, giveRating);
export default router;
