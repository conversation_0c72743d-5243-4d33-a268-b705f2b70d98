import validator from "validator";
import { News } from "./newsModal.js";

export const getNews = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let { page, email } = req.query;
    page = page || 1;
    let limitVal = 25;
    let skipValue = (page - 1) * limitVal;
    let query = {};
    if (email) {
      query.email = { $regex: email, $options: "i" };
    }
    const totalResults = await News.countDocuments(query);
    const data = await News.find(query)
      .skip(skipValue)
      .limit(limitVal)
      .sort({ createdAt: -1 });
    return res.status(200).json({ data, totalResults });
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const addNewsLetter = async (req, res) => {
  try {
    const { email } = req.body;
    if (!email) {
      return res.status(400).json({ error: "Email is required" });
    }
    if (email && !validator.isEmail(email)) {
      return res.status(400).json({ error: "Invalid email address" });
    }
    const news = await News.findOne({ email });
    if (news) {
      return res.status(400).json({ error: "Already Subscribed" });
    }
    const data = await News.create({
      email,
    });
    return res.status(200).json(data);
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};
