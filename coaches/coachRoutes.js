import { Router } from "express";
import {
  getCoaches,
  getCoachById,
  createCoach,
  updateCoachData,
  changeStatus,
  changeAuthStatus,
  uploadFile,
  coachLogin,
  dashboardLogs,
  getCoachByPlayerId,
  deleteCoach,
  requestPasswordReset,
  resetpassword,
} from "./coachController.js";
import { coachProtect, flexibleAuth } from "../utils/auth.js";
import { downloadFile } from "../utils/awsHelper.js";
import authenticateUserAccess from "../Helpers/authenticationUser.js";

const router = Router();

router.post("/login", coachLogin);
router
  .route("/")
  .get(flexibleAuth("coach", "read"), getCoaches)
  .post(flexibleAuth("coach", "write", true), createCoach);
router
  .route("/:id")
  .get(flexibleAuth("coach", "read"), coachProtect, getCoachById)
  .patch(
    flexibleAuth("coach", "write"),
    coachProtect,
    updateCoachData
  )
  .delete(flexibleAuth("coach", "delete"), coachProtect, deleteCoach);
router
  .route("/player/:id")
  .get(flexibleAuth("coach", "read", true), getCoachByPlayerId);

router
  .route("/updateStatus/:id")
  .patch(flexibleAuth("coach", "write"), changeStatus);
router
  .route("/updateAuthStatus/:id")
  .patch(flexibleAuth("coach", "write"), changeAuthStatus);
router.route("/uploadImage/").post(uploadFile);
router.route("/download/").post(downloadFile);
router.route("/dashboard").post(coachProtect, dashboardLogs);
router.route("/requestResetPassword/:email").post(requestPasswordReset);
router.route("/resetPassword/").post(resetpassword);

export default router;
