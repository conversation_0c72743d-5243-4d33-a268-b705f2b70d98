import { Coach } from "./coachModal.js";
import validator from "validator";
import { multerUpload } from "../utils/awsHelper.js";
import { SECRETS } from "../utils/config.js";
import AWS from "aws-sdk";
import bcrypt from "bcryptjs";
import crypto from "crypto";
import { newToken } from "../utils/jwt.js";
import { Course } from "../courses/courseModal.js";
import { Booking } from "../bookings/bookingModal.js";
import { Events } from "../events/eventModal.js";
import { sendEmail, sendSms } from "../utils/msg.js";
import { Academy } from "../academyModule/academy/academyModel.js";
import { daysDifference, formatDateToYYYYMMDD } from "../utils/datehelper.js";
import { createBookingEvents } from "../calendar/calendarController.js";
import { getDatesByDays } from "../courses/courseController.js";
import moment from "moment";
import { AcademyTopCoach } from "../academyModule/cms/model/academyTopCoachModal.js";

const AWS_ACCESS_KEY_ID = SECRETS.AWS_ACCESS_KEY_ID;
const AWS_SECRET_ACCESS_KEY = SECRETS.AWS_SECRET_ACCESS_KEY;
const AWS_BUCKET_NAME = SECRETS.AWS_BUCKET_NAME;
const AWS_S3_REGION = SECRETS.AWS_S3_REGION;

AWS.config.update({
  accessKeyId: AWS_ACCESS_KEY_ID,
  secretAccessKey: AWS_SECRET_ACCESS_KEY,
  region: AWS_S3_REGION,
});
const s3 = new AWS.S3();

export const coachLogin = async (req, res) => {
  try {
    const { email, password } = req.body;
    if (!email || !password) {
      return res.status(400).json({ error: "Please Enter Credentials" });
    }
    const user = await Coach.findOne({ email });
    if (!user) {
      return res.status(400).json({ error: "User not registered" });
    } else {
      const passwordCompare = await bcrypt.compare(password, user.password);
      if (!passwordCompare) {
        return res
          .status(400)
          .json({ error: "Please try to login with correct credentials" });
      } else {
        const token = newToken(user, "coach");
        return res
          .status(200)
          .json({ status: "ok", token: token, id: user._id });
      }
    }
  } catch (e) {
    res.status(500).json({ error: "Internal server error" });
  }
};

export const getCoaches = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let {
      page,
      firstName,
      status,
      authStatus,
      getAllCoaches = true,
    } = req.query;
    page = page || 1;
    let limitVal = 25;
    let skipValue = (page - 1) * limitVal;
    let query = {};

    if (req.userType === "academy" && req.user?.academyId?._id) {
      query.academyId = req.user.academyId._id;
    } else if (req.userType === "Admin" && !getAllCoaches) {
      query.$or = [
        { academyId: { $exists: false } },
        { academyId: null },
        { academyId: "" },
      ];
    }

    if (firstName) {
      query.firstName = { $regex: firstName, $options: "i" };
    }
    if (status) {
      query.status = status;
    }
    if (authStatus) {
      query.authStatus = authStatus;
    }

    const totalResults = await Coach.countDocuments(query);
    const data = await Coach.find(query)
      .skip(skipValue)
      .limit(limitVal)
      .select("-password")
      .populate("academyId", "name")
      .sort({ createdAt: -1 });
    if (!data) {
      return res.status(200).json({ error: "User doesn't exist" });
    }
    return res.status(200).json({ data, totalResults, length: data.length });
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const getCoachById = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "id is required" });
    }

    if (req.userType === "academy" && req.user?.academyId?._id) {
      const data = await Coach.findOne({
        _id: id,
        academyId: req.user.academyId._id,
      }).select("-password");
      if (!data) {
        return res
          .status(200)
          .json({ error: "Coach not found or not authorized" });
      }
      return res.status(200).json(data);
    } else if (req.userType === "coach") {
      if (req.user._id.toString() !== id.toString()) {
        return res
          .status(400)
          .json({ error: "You can not access other coach data" });
      }
    }

    const data = await Coach.findById(id).select("-password");
    if (!data) {
      return res.status(200).json({ error: "Coach doesn't exist" });
    }
    res.status(200).json(data);
  } catch (e) {
    res.status(500).json({ error: "Internal server error" });
  }
};

export const getCoachByPlayerId = async (req, res) => {
  try {
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "id is required" });
    }
    const data = await Coach.findById(id).select("-password");
    if (!data) {
      return res.status(200).json({ error: "User doesn't exist" });
    }
    res.status(200).json(data);
  } catch (e) {
    res.status(500).json({ error: "Internal server error" });
  }
};

export const createCoach = async (req, res) => {
  try {
    const {
      email,
      password,
      firstName,
      lastName,
      mobile,
      hasGst,
      gstNumber,
      affiliationType,
      academyId,
      coachShare,
      academyShare,
      academyAvailability,
    } = req.body;

    // Check if required fields are present
    if (!firstName || !lastName || !mobile || !affiliationType) {
      return res.status(400).json({
        error:
          "First name, last name, mobile, and affiliationType are required fields",
      });
    }

    if (req.userType === "academy" && req.user?.academyId?._id) {
      if (affiliationType !== "academy") {
        return res.status(400).json({
          error: "You can not create coach without affiliationType academy",
        });
      }
      if (!academyId) {
        return res.status(400).json({ error: "Academy ID is required" });
      }
      if (req.user.academyId._id.toString() !== academyId.toString()) {
        return res
          .status(400)
          .json({ error: "You can not create coach for other academy" });
      }
      const academyCheck = await Academy.findById(req.user.academyId._id);
      if (!academyCheck) {
        return res.status(400).json({ error: "Academy not found" });
      }
      if (academyCheck.status !== "active") {
        return res.status(400).json({ error: "Academy is not active" });
      }
      if (academyCheck.authStatus !== "authorized") {
        return res.status(400).json({ error: "Academy is not authorized" });
      }
    }

    // Validate field lengths
    let academy;
    if (affiliationType !== "individual") {
      if (
        !academyId ||
        coachShare === undefined ||
        coachShare === null ||
        academyShare === undefined ||
        academyShare === null ||
        !academyAvailability
      ) {
        return res.status(400).json({
          error:
            "academyId, coachShare, academyShare, and academyAvailability are required fields",
        });
      } else {
        academy = await Academy.findById(academyId);
        if (!academy) {
          return res.status(400).json({ error: "Academy not found" });
        }
        if (academy.status !== "active") {
          return res.status(400).json({ error: "Academy is not active" });
        }
      }
    }

    if (!validator.isLength(firstName, { min: 3 })) {
      return res
        .status(400)
        .json({ error: "First name must have at least 3 characters" });
    }

    if (!validator.isLength(lastName, { min: 3 })) {
      return res
        .status(400)
        .json({ error: "Last name must have at least 3 characters" });
    }

    // Validate mobile number
    if (!validator.isMobilePhone(mobile.toString(), "en-IN")) {
      return res.status(400).json({ error: "Invalid phone number" });
    }

    // Validate email if provided
    if (email && !validator.isEmail(email)) {
      return res.status(400).json({ error: "Invalid email address" });
    }

    // Validate password if provided
    if (
      password &&
      (!validator.isLength(password, { min: 6 }) ||
        typeof password !== "string")
    ) {
      return res.status(400).json({
        error: "Password must be a non-empty string with at least 6 characters",
      });
    }
    if (hasGst && !gstNumber) {
      return res
        .status(400)
        .json({ error: "GST number is required if GST is applicable" });
    }
    // Check if user already exists
    const user = await Coach.find({ $or: [{ email }, { mobile }] });
    if (user.length > 0) {
      return res
        .status(400)
        .json({ error: "Coach with same mobile or email already exists" });
    }

    // Hash the password if provided
    const salt = password ? await bcrypt.genSalt(10) : undefined;
    const secPass = password ? await bcrypt.hash(password, salt) : undefined;

    // Calculate IST time
    const currentUTC = new Date();
    const ISTOffset = 5.5; // IST offset is 5 hours and 30 minutes
    const ISTTime = new Date(currentUTC.getTime() + ISTOffset * 60 * 60 * 1000);

    // Format IST time in the desired format
    const formattedIST = ISTTime.toISOString();

    // Dates Validation
    // Validate start date and end date

    if (affiliationType === "academy") {
      const dateHelper = await getDatesByDays(
        new Date(academyAvailability.startDate.split("T")[0]),
        new Date(academyAvailability.endDate.split("T")[0]),
        academyAvailability.days
      );
      const isValidStartDate = moment(
        academyAvailability.startDate
      ).isSameOrAfter(moment(), "day");
      const isValidEndDate = moment(academyAvailability.endDate).isSameOrAfter(
        moment(),
        "day"
      );
      const isValidDateRange = moment(
        academyAvailability.endDate
      ).isSameOrAfter(moment(academyAvailability.startDate), "day");

      // Validate start time and end time
      const isValidStartTime = moment(
        academyAvailability.startTime,
        "HH:mm",
        true
      ).isValid();
      const isValidEndTime = moment(
        academyAvailability.endTime,
        "HH:mm",
        true
      ).isValid();

      // Validate days array
      const validDays = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
      const isValidDays = academyAvailability.days.every((dayObj) =>
        validDays.includes(dayObj)
      );

      if (
        !isValidStartDate ||
        !isValidEndDate ||
        !isValidDateRange ||
        !isValidStartTime ||
        !isValidEndTime ||
        !isValidDays
      ) {
        return res.status(400).json({ error: "Invalid date range or time" });
      }

      academyAvailability.dates = dateHelper;
    }

    // Create coach object
    let addObj = {
      ...req.body,
      firstName,
      lastName,
      mobile,
      email: email || undefined, // Set to undefined if not provided
      password: secPass,
      registrationDate: formattedIST,
      authStatus: req.auth ? "authorized" : "unauthorized",
      status: "inactive",
    };

    // Remove academy fields if affiliationType is individual
    if (affiliationType === "individual") {
      delete addObj.academyId;
      delete addObj.coachShare;
      delete addObj.academyShare;
      delete addObj.academyAvailability;
    }

    // Create coach in the database
    const data = await Coach.create(addObj);

    // If academy user is creating coach, add to AcademyTopCoach
    if (req.user?.academyId?._id) {
      try {
        const topCoachEntry = await AcademyTopCoach.create({
          academy: req.user.academyId._id,
          coach: data._id,
        });
      } catch (err) {
        console.error("Error message:", err.message);
      }
    }

    // Exclude the password field from the response
    const responseData = data.toObject();
    delete responseData.password;

    // Generate and return token (replace 'user' with appropriate data)
    const token = newToken(responseData, "coach");
    await sendEmail(
      firstName,
      email,
      "coach_sign_up",
      `{"Coach": "${firstName}"}`
    );

    // signup sms for the coach
    await sendSms("6791e511d6fc055d383abf42", "1", responseData.mobile);
    return res.status(200).json({ data: responseData, token });
  } catch (e) {
    console.error(e);
    res.status(500).json({ error: "Internal server error" });
  }
};

export const updateCoachData = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "id is required" });
    }
    const {
      email,
      password,
      firstName,
      lastName,
      mobile,
      hasGst,
      gstNumber,
      affiliationType,
      academyId,
      coachShare,
      academyShare,
      academyAvailability,
    } = req.body;
    if (req.userType === "academy" && req.user?.academyId?._id) {
      const coach = await Coach.findOne({
        _id: id,
        academyId: req.user.academyId._id,
      });
      if (!coach) {
        return res
          .status(403)
          .json({ error: "Coach not found or not authorized" });
      }
    } else if (req.userType === "Admin") {
      const coach = await Coach.findById(id);
      if (!coach || coach.affiliationType === "academy") {
        return res
          .status(400)
          .json({ error: "Coach not found or not authorized to update" });
      }
    }
    if (affiliationType === "academy") {
      if (
        !academyId ||
        coachShare === undefined ||
        coachShare === null ||
        academyShare === undefined ||
        academyShare === null ||
        !academyAvailability
      ) {
        return res.status(400).json({
          error:
            "academyId, coachShare, academyShare, and academyAvailability are required fields",
        });
      }
      const academy = await Academy.findById(academyId);
      if (!academy) {
        return res.status(400).json({ error: "Academy not found" });
      }
      if (academy.status !== "active") {
        return res.status(400).json({ error: "Academy is not active" });
      }
      const dateHelper = await getDatesByDays(
        new Date(academyAvailability.startDate.split("T")[0]),
        new Date(academyAvailability.endDate.split("T")[0]),
        academyAvailability.days
      );
      const isValidStartDate = moment(
        academyAvailability.startDate
      ).isSameOrAfter(moment(), "day");
      const isValidEndDate = moment(academyAvailability.endDate).isSameOrAfter(
        moment(),
        "day"
      );
      const isValidDateRange = moment(
        academyAvailability.endDate
      ).isSameOrAfter(moment(academyAvailability.startDate), "day");

      // Validate start time and end time
      const isValidStartTime = moment(
        academyAvailability.startTime,
        "HH:mm",
        true
      ).isValid();
      const isValidEndTime = moment(
        academyAvailability.endTime,
        "HH:mm",
        true
      ).isValid();

      // Validate days array
      const validDays = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
      const isValidDays = academyAvailability.days.every((dayObj) =>
        validDays.includes(dayObj)
      );

      if (
        !isValidStartDate ||
        !isValidEndDate ||
        !isValidDateRange ||
        !isValidStartTime ||
        !isValidEndTime ||
        !isValidDays
      ) {
        return res.status(400).json({ error: "Invalid date range or time" });
      }

      academyAvailability.dates = dateHelper;
    }
    if (email) {
      return res.status(400).json({ error: "Can not change email" });
    }
    if (password) {
      return res.status(400).json({ error: "Can not change password" });
    }
    if (firstName && !validator.isLength(firstName, { min: 3 })) {
      return res
        .status(400)
        .json({ error: "First name must have at least 3 characters" });
    }

    if (lastName && !validator.isLength(lastName, { min: 3 })) {
      return res
        .status(400)
        .json({ error: "Last name must have at least 3 characters" });
    }

    if (mobile && !validator.isMobilePhone(mobile, "en-IN")) {
      return res.status(400).json({ error: "Invalid phone number" });
    }

    if (hasGst && !gstNumber) {
      return res
        .status(400)
        .json({ error: "GST number is required if GST is applicable" });
    }
    const existingUser = await Coach.findOne({
      _id: { $ne: id },
      mobile,
    });

    if (existingUser) {
      return res.status(300).json({ error: "mobile already exists" });
    }
    const updateObj = {
      ...req.body,
    };
    const data = await Coach.findByIdAndUpdate(id, updateObj, { new: true });
    const responseData = data.toObject();
    delete responseData.password;
    res.status(200).json(responseData);
  } catch (e) {
    console.error(e);
    res.status(500).json({ error: "Internal server error" });
  }
};

export const changeStatus = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "id is required" });
    }

    if (req.userType === "academy" && req.user?.academyId?._id) {
      const coach = await Coach.findOne({
        _id: id,
        academyId: req.user.academyId._id,
      });
      if (!coach) {
        return res
          .status(403)
          .json({ error: "Coach not found or not authorized" });
      }
    } else if (req.userType === "Admin") {
      const coach = await Coach.findById(id);
      if (!coach || coach?.affiliationType === "academy") {
        return res
          .status(400)
          .json({ error: "Coach not found or not authorized to update status" });
      }
    }

    const { status } = req.body;
    if (
      status === "active" ||
      status === "inactive" ||
      status === "deactivated"
    ) {
      const data = await Coach.findByIdAndUpdate(
        id,
        { status },
        {
          new: true,
          upsert: false,
        }
      );
      await Course.updateMany({ coach_id: id }, { coachStatus: status });
      if (data) {
        res.status(200).json({ message: "Status updated successfully" });
      } else {
        res.status(400).json({ error: "User not found" });
      }
    } else {
      res.status(400).json({ error: "status is not defined" });
    }
  } catch (e) {
    res.status(500).json({ error: "Internal server error" });
  }
};

export const changeAuthStatus = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "id is required" });
    }

    if (req.userType === "academy" && req.user?.academyId?._id) {
      const coach = await Coach.findOne({
        _id: id,
        academyId: req.user.academyId._id,
      });
      if (!coach) {
        return res
          .status(403)
          .json({ error: "Coach not found or not authorized" });
      }
    } else if (req.userType === "Admin") {
      const coach = await Coach.findById(id);
      if (!coach || coach?.affiliationType === "academy") {
        return res
          .status(400)
          .json({ error: "Coach not found or not authorized to update auth status" });
      }
    }

    const { authStatus } = req.body;
    if (authStatus === "authorized" || authStatus === "unauthorized") {
      const data = await Coach.findByIdAndUpdate(
        id,
        { authStatus, approvalDate: new Date(), status: "active" },
        {
          new: true,
          upsert: false,
        }
      );
      if (data) {
        res.status(200).json({ message: "Status updated successfully" });
      } else {
        res.status(400).json({ error: "User not found" });
      }
    } else {
      res.status(400).json({ error: "status is not defined" });
    }
  } catch (e) {
    res.status(500).json({ error: "Internal server error" });
  }
};

export const uploadFile = async (req, res) => {
  try {
    const uploadSingle = multerUpload(AWS_BUCKET_NAME, "images2/coach").single(
      "image"
    );

    uploadSingle(req, res, async (err) => {
      if (err) {
        return res.status(400).json({ error: err.message });
      }
      const url = req.body.url;
      if (url) {
        const deleteParams = {
          Bucket: AWS_BUCKET_NAME,
          Key: `images2/coach/${url.split("/").pop()}`,
        };
        const deleteResult = await s3.deleteObject(deleteParams).promise();
      }
      // Assuming your uploaded file is public
      // const publicUrl = `${AWS_S3_BASE_URL}/${AWS_BUCKET_NAME}/${req?.file?.key}`;

      return res.status(200).json({ url: req?.file?.location });
    });
  } catch (error) {
    res.status(500).json({
      error: error.message || "Internal server error",
    });
  }
};

export const dashboardLogs = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let { id, date } = req.body;

    if (!id || !date) {
      return res.status(400).json({ error: "Id and date not found" });
    }

    const startDate = new Date(date);
    const endDate = new Date(date);
    endDate.setDate(endDate.getDate() + 1); // Next day at midnight
    const startDate1 = `${date}T00:00:00.000Z`;
    const endDate1 = `${date}T23:59:59.000Z`;
    const coursesAndClasses = await Course.find({
      coach_id: id,
      "dates.dates": date,
    });
    const courseIds = coursesAndClasses.map((item) => item._id);

    const courseBookings = await Booking.find({
      courseId: { $in: courseIds },
      "classes.date": { $gte: startDate1, $lt: endDate1 },
    });

    const events = await Events.find({
      coachId: id,
      dates: date,
    });

    let bookingClass = [];

    // Create bookingClass array with individual bookings for each class
    courseBookings.forEach((booking) => {
      booking.classes.forEach((cls) => {
        bookingClass.push({
          bookingId: booking._id,
          booking: booking.bookingId,
          orderId: booking.orderId,
          coachId: booking.coachId,
          courseId: booking.courseId,
          courseName: booking.courseName,
          player: booking.player,
          playerName: booking.playerName,
          playerEmail: booking.playerEmail,
          courseType: booking.courseType,
          classId: cls._id,
          classDate: cls.date,
          classStartTime: cls.startTime,
          classEndTime: cls.endTime,
          duration: cls.duration,
          days: cls.days,
          fees: cls.fees,
          status: cls.status,
          cancellationDate: cls.cancellationDate,
          eventId: cls.eventId,
          attendance: cls.attendance,
          coachAttendance: cls.coachAttendance,
        });
      });
    });

    let finalData = [];
    // Add events to final data
    events.forEach((event) => {
      finalData.push({
        type: "event",
        name: event.name,
        startTime: event.startTime,
        endTime: event.endTime,
        facility: "",
        coursePrice: "",
        playerEnrolled: "",
        maxGroupSize: "",
        image: "",
        bookings: [],
      });
    });
    // Filter bookingClass array based on course ID and date/time
    coursesAndClasses.forEach((item) => {
      const itemBookings = bookingClass.filter((booking) => {
        return (
          booking.courseId.equals(item._id) &&
          booking.classDate >= startDate &&
          booking.classDate < endDate
        );
      });

      finalData.push({
        type: item.classType,
        name: item.courseName,
        startTime: item.dates.startTime,
        endTime: item.dates.endTime,
        facility: item.facility.name,
        coursePrice: item.fees.fees,
        playerEnrolled: item.playerEnrolled,
        maxGroupSize: item.maxGroupSize,
        image: item.images[0]?.url || "",
        id: item._id,
        bookings: itemBookings.map((booking) => ({
          id: booking.bookingId,
          name: booking.playerName,
          playerId: booking.player,
          startTime: booking.classStartTime,
          endTime: booking.classEndTime,
          pricePaid: booking.fees,
          classId: booking.classId,
          attendance: booking.attendance || "NA",
          coachAttendance: booking.coachAttendance || "NA",
          status: booking.status || "upcoming",
        })),
      });
    });

    // Sort final data by startTime
    finalData.sort((a, b) => {
      const startTimeA = new Date(`${date}T${a.startTime}`);
      const startTimeB = new Date(`${date}T${b.startTime}`);
      return startTimeA - startTimeB;
    });

    // Sort bookings within each item by startTime
    finalData.forEach((item) => {
      item.bookings.sort((a, b) => {
        const startTimeA = new Date(`${date}T${a.startTime}`);
        const startTimeB = new Date(`${date}T${b.startTime}`);
        return startTimeA - startTimeB;
      });
    });

    return res.status(200).json(finalData);
  } catch (e) {
    console.error(e);
    return res.status(500).json({ error: "Internal Server Error" });
  }
};

// delete coach
export const deleteCoach = async (req, res) => {
  try {
    const id = req.params.id;
    if (req.userType === "academy" && req.user?.academyId?._id) {
      const coach = await Coach.findOne({
        _id: id,
        academyId: req.user.academyId._id,
      });
      if (!coach) {
        return res
          .status(400)
          .json({ error: "Coach not found or not authorized" });
      }
    } else if (req.userType === "Admin") {
      const coach = await Coach.findById(id);
      if (!coach || coach.affiliationType === "academy") {
        return res
          .status(400)
          .json({ error: "Coach not found or not authorized to delete" });
      }
    }
    const courses = await Course.find({ coach_id: id });
    if (courses.length > 0) {
      return res
        .status(400)
        .json({ error: "Coach with courses can't be deleted" });
    }
    await Coach.findByIdAndDelete(id);
    return res.status(200).json({ message: "Coach deleted successfully" });
  } catch (e) {
    return res.status(500).json({
      error: `Internal server Error ${e}`,
    });
  }
};

// request password reset
export const requestPasswordReset = async (req, res) => {
  try {
    const { email } = req.params;
    const user = await Coach.findOne({ email });
    if (!user) {
      return res.status(400).json({ error: "User not found" });
    }

    const token = crypto.randomBytes(32).toString("hex");
    const tokenExpiry = Date.now() + 3600000; // Token valid for 1 hour

    user.resetPasswordToken = token;
    user.resetPasswordExpires = tokenExpiry;
    await user.save();
    await sendEmail(
      user.firstName,
      email,
      "reset_password_14",
      `{"Name": "${user.firstName}", "Reset_Password_Link":"${SECRETS.coachBaseUrl}/resetPassword?token=${token}"}`
    );
    return res
      .status(200)
      .json({ message: "resent password link sent successfully" });
  } catch (e) {
    return res
      .status(500)
      .json({ error: `Internal server error : ${e.message}` });
  }
};

// reset password
export const resetpassword = async (req, res) => {
  try {
    const { token, newPassword } = req.body;
    const user = await Coach.findOne({
      resetPasswordToken: token,
      resetPasswordExpires: { $gt: Date.now() },
    });

    if (!user) {
      return res.status(400).json({ error: "Invalid or expired token" });
    }
    if (
      newPassword &&
      (!validator.isLength(newPassword, { min: 6 }) ||
        typeof newPassword !== "string")
    ) {
      return res.status(400).json({
        error: "Password must be a non-empty string with at least 6 characters",
      });
    }
    const salt = newPassword ? await bcrypt.genSalt(10) : undefined;
    const secPass = newPassword
      ? await bcrypt.hash(newPassword, salt)
      : undefined;

    user.password = secPass; // Make sure to hash the password before saving
    user.resetPasswordToken = undefined;
    user.resetPasswordExpires = undefined;
    await user.save();

    return res.status(200).json({ message: "Password reset successfully" });
  } catch (e) {
    return res
      .status(500)
      .json({ error: `Internal server error : ${e.message}` });
  }
};
