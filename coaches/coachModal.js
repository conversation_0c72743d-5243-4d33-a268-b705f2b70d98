import mongoose from "mongoose";
const { Schema, model } = mongoose;

const coachSchema = new Schema(
  {
    firstName: {
      required: true,
      type: String,
    },
    lastName: {
      required: true,
      type: String,
    },
    mobile: {
      required: true,
      type: String,
      unique: true,
    },
    email: {
      type: String,
      unique: true,
    },
    password: {
      type: String,
    },
    gender: {
      type: String,
      enum: ["male", "female", "other"],
    },
    gstState: {
      type: String,
    },
    age: {
      type: String,
    },
    dob: {
      type: Date,
    },
    profileImg: {
      type: String,
    },
    alternateMobile: {
      type: String,
    },
    linkedFacilities: [
      {
        name: {
          type: String,
        },
        addressLine1: {
          type: String,
        },
        addressLine2: {
          type: String,
        },
        city: {
          type: String,
        },
        state: {
          type: String,
        },
        pinCode: {
          type: String,
        },
        country: {
          type: String,
        },
        amenities: {
          type: String,
        },
        location: {
          type: {
            type: String,
            default: "Point",
          },
          coordinates: [],
          is_location_exact: {
            type: Boolean,
            default: true,
          },
        },
      },
    ],
    status: {
      type: String,
      default: "inactive",
      enum: ["active", "inactive", "deactivated"],
    },
    authStatus: {
      type: String,
      required: true,
      default: "unauthorized",
      enum: ["authorized", "unauthorized"],
    },
    experience: {
      type: Number,
    },
    language: {
      type: String,
    },
    sportsCategories: [
      {
        type: String,
      },
    ],
    coachingQualifications: [
      {
        description: {
          type: String,
        },
        image: {
          type: String,
        },
      },
    ],
    coachingExperience: [
      {
        description: {
          type: String,
        },
        image: {
          type: String,
        },
      },
    ],
    playerExperience: [
      {
        description: {
          type: String,
        },
        image: {
          type: String,
        },
      },
    ],
    award: [
      {
        description: {
          type: String,
        },
        image: {
          type: String,
        },
      },
    ],
    registrationDate: {
      type: Date,
    },
    approvalDate: {
      type: Date,
    },
    ratings: {
      type: Number,
      default: 0,
    },
    kycDocuments: {
      documentName: {
        type: String,
      },
      documentNumber: {
        type: String,
      },
      documentImg: [
        {
          url: { type: String },
        },
      ],
    },
    aadhaarNumber: {
      type: String,
    },
    aadhaarImage: {
      type: [String],
      default: [],
    },
    bankDetails: {
      accountNumber: {
        type: String,
      },
      accountHolderName: {
        type: String,
      },
      ifsc: {
        type: String,
      },
    },
    googleEmail: {
      type: String,
    },
    refreshToken: {
      // required: true,
      type: String,
    },
    resetPasswordToken: {
      type: String,
    },
    resetPasswordExpires: {
      type: Date,
    },
    hasGst: {
      type: Boolean,
      default: false,
    },
    gstNumber: {
      type: String,
    },
    privacyPolicyAccepted: {
      type: Boolean,
      default: true,
    },
    affiliationType: {
      type: String,
      enum: ["academy", "individual"],
    },
    // academyId is used to link the coach to an academy
    academyId: {
      type: Schema.Types.ObjectId,
      ref: "academy",
    },
    coachShare: {
      type: Number,
      default: 0,
    },
    academyShare: {
      type: Number,
      default: 0,
    },
    academyAvailability: {
      eventId: {
        type: String,
      },
      startDate: {
        type: Date,
        required: function () {
          return this.affiliationType === "academy";
        },
      },
      endDate: {
        type: Date,
        required: function () {
          return this.affiliationType === "academy";
        },
      },
      startTime: {
        type: String,
        required: function () {
          return this.affiliationType === "academy";
        },
      },
      endTime: {
        type: String,
        required: function () {
          return this.affiliationType === "academy";
        },
      },
      days: {
        type: [String],
        required: function () {
          return this.affiliationType === "academy";
        },
      },
      dates: [
        {
          type: Date,
        },
      ],
    },
  },
  { timestamps: true }
);

coachSchema.index({ email: 1, mobile: 1 }, { unique: true });

export const Coach = model("coach", coachSchema);
