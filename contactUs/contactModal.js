import mongoose from "mongoose";
const { Schema, model } = mongoose;

const contactSchema = new Schema(
  {
    firstName: {
      type: String,
      required: true,
    },
    lastName: {
      type: String,
      required: true,
    },
    mobile: {
      type: String,
      required: true,
    },
    email: {
      type: String,
      required: true,
    },
    message: {
      type: String,
      required: true,
    },
    userType: {
      type: String,
      required: true,
      enum: ["player", "coach", "academy"],
    },
    status: {
      type: String,
      default: "active",
      enum: ["active", "inactive"],
    },
    Date: {
      type: Date,
      default: new Date(),
    },
  },
  { timestamps: true }
);

export const Contact = model("contact", contactSchema);
