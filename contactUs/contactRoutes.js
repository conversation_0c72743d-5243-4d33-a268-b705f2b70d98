import { Router } from "express";
import authenticateUserAccess from "../Helpers/authenticationUser.js";
import {
  changeStatus,
  createContactUs,
  getContactById,
  getContacts,
} from "./contactController.js";

const router = Router();

router
  .route("/")
  .get(authenticateUserAccess("contact", "read"), getContacts)
  .post(createContactUs);

router
  .route("/:id")
  .get(authenticateUserAccess("contact", "read"), getContactById)
  .patch(authenticateUserAccess("contact", "read"), changeStatus);

export default router;
