import validator from "validator";
import { Contact } from "./contactModal.js";

export const getContacts = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let { page, status, userType } = req.query;
    page = page || 1;
    let limitVal = 25;
    let skipValue = (page - 1) * limitVal;
    let query = {};
    if (status) {
      query.status = status;
    }
    if (userType) {
      query.userType = userType;
    }
    const totalResults = await Contact.countDocuments(query);
    const data = await Contact.find(query)
      .skip(skipValue)
      .limit(limitVal)
      .sort({ createdAt: -1 });
    return res.status(200).json({ data, totalResults });
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const getContactById = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "id is required" });
    }
    const data = await Contact.findById(id);
    return res.status(200).json(data);
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const createContactUs = async (req, res) => {
  try {
    const { firstName, lastName, mobile, email, message, userType } = req.body;
    if (!firstName || !lastName || !mobile || !email || !message || !userType) {
      return res.status(400).json({
        error:
          "First name, last name, email, mobile, message, and userType are required fields",
      });
    }
    if (firstName && !validator.isLength(firstName, { min: 3 })) {
      return res
        .status(400)
        .json({ error: "First name must have at least 3 characters" });
    }

    if (lastName && !validator.isLength(lastName, { min: 3 })) {
      return res
        .status(400)
        .json({ error: "Last name must have at least 3 characters" });
    }

    if (mobile && !validator.isMobilePhone(mobile.toString(), "en-IN")) {
      return res.status(400).json({ error: "Invalid phone number" });
    }

    if (email && !validator.isEmail(email)) {
      return res.status(400).json({ error: "Invalid email address" });
    }
    if (message && !validator.isLength(message, { min: 10 })) {
      return res
        .status(400)
        .json({ error: "message must have at least 10 characters" });
    }

    const data = await Contact.create({
      firstName,
      lastName,
      mobile,
      email,
      message,
      userType,
    });
    return res.status(200).json(data);
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const changeStatus = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "id is required" });
    }
    const { status } = req.body;
    if (status === "active" || status === "inactive") {
      await Contact.findByIdAndUpdate(
        id,
        { status },
        {
          new: true,
          upsert: false,
        }
      );
    }
    return res.status(200).json({ message: "Status Updated Successfully" });
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};
