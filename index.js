import express, { urlencoded, json } from "express";
import cron from "node-cron";
import { connect } from "./utils/db.js";
import { SECRETS } from "./utils/config.js";
import { courseCron } from "./courses/courseController.js";
import { bookingCron } from "./bookings/bookingController.js";
import globalErrorHandler from "./middlewares/globalErrorHandler.js";
import AppError from "./middlewares/appError.js";
import indexRouter from "./indexRoutes.js";
import cookieParser from "cookie-parser";
import corsMiddleware from "./utils/cors.js";

const PORT = SECRETS.port;
const app = express();
app.use(corsMiddleware);

app.use(json());
app.use(urlencoded({ extended: true }));
app.use(cookieParser());

app.get("/healthcheck", (req, res) => {
  console.log("Healthcheck OK -- with EB");
  res.json("Healthcheck OK -- with EB");
});

app.use("/api", indexRouter);

cron.schedule("0 1 * * *", courseCron, {
  scheduled: true,
  timezone: "Asia/Kolkata",
});
cron.schedule("0 2 * * *", bookingCron, {
  scheduled: true,
  timezone: "Asia/Kolkata",
});

app.all("/*splat", (req, res, next) => {
  next(
    new AppError(`<Custom Stack Trace Error Message>`, 404, {
      errors: [
        {
          field: "path",
          message: `Can't find ${req.originalUrl} on this server!`,
        },
      ],
    })
  );
});

app.use(globalErrorHandler);

export const start = async () => {
  try {
    connect();
    app.listen(PORT, () => {
      console.log(`REST API on http://localhost:${PORT}`);
    });
    // app.listen(PORT, () => {
    //   console.log(`Server is running on http://0.0.0.0:${PORT}`);
    // });
  } catch (e) {
    console.log(e);
  }
};

start();
