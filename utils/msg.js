import axios from "axios";
import { SECRETS } from "./config.js";

export const sendSms = async (templateId,route, mobile, var1, var2, var3) => {
  
  let data;
  if (var1 === null && var2 === null) {
    data = `{
        "template_id": "${templateId}",
        "route":"${route}",
        "recipients": [
          {
            "mobiles": "91${mobile}"
          }
        ]
      }`;
  } else if (var1 !== null && var2 === null) {
    data = `{
        "template_id": "${templateId}",
        "route":"${route}",
        "recipients": [
          {
            "mobiles": "91${mobile}",
            "VAR1": "${var1}"
          }
        ]
      }`;
  } else if(var1  && var2  && var3 ){
    data = `{
      "template_id": "${templateId}",
      "route":"${route}",
      "recipients": [
        {
          "mobiles": "91${mobile}",
          "coachname": "${var1}",
          "date": "${var2}",
          "amount": "${var3}"
        }
      ]
    }`;
  }  else if(var1  && var2  && var3 === false && route === "4"){
    data = `{
      "template_id": "${templateId}",
      "route":"${route}",
      "recipients": [
        {
          "mobiles": "91${mobile}",
          "amount": "${var1}",
          "payment method name": "${var2}"
        }
      ]
    }`;
  }else if(var1  && var2  && var3 === null && route === "4"){
    data = `{
      "template_id": "${templateId}",
      "route":"${route}",
      "recipients": [
        {
          "mobiles": "91${mobile}",
          "name": "${var1}",
          "date": "${var2}"
        }
      ]
    }`;
  }else {
    data = `{
        "template_id": "${templateId}",
        "route":"${route}",
        "recipients": [
          {
            "mobiles": "91${mobile}",
            "VAR1": "${var1}",
            "VAR2": "${var2}"
          }
        ]
      }`;
  }
  try {
    const response = await axios.post(
      "https://control.msg91.com/api/v5/flow",
      data,
      {
        headers: {
          accept: "application/json",
          authkey: `${SECRETS.msgAuthKey}`,
          "content-type": "application/json",
        },
      }
    );
  } catch (error) {
    // console.error(error);
  }
};

export const sendEmail = async (name, email, templateId, variables, pdfUrl) => {
  let data;
  if(pdfUrl)
  {
     data = `{
      "recipients": [
          {
              "to": [
                  {
                      "name": "${name}",
                      "email": "${email}"
                  }
              ],
              "variables": ${variables}
          }
      ],
      "from": {
          "name": "${SECRETS.senderName}",
          "email": "${SECRETS.senderEmail}"
      },
      "attachments": [
        {
          "filePath": "${pdfUrl}",
          "fileName": "order-summary.pdf"
        }
      ],
      "domain": "${SECRETS.senderDomain}",
      "template_id": "${templateId}"
  }`;
  }else{
   data = `{
      "recipients": [
          {
              "to": [
                  {
                      "name": "${name}",
                      "email": "${email}"
                  }
              ],
              "variables": ${variables}
          }
      ],
      "from": {
          "name": "${SECRETS.senderName}",
          "email": "${SECRETS.senderEmail}"
      },
      "domain": "${SECRETS.senderDomain}",
      "template_id": "${templateId}"
  }`;
  }
  
  try {
    const response = await axios.post(
      "https://control.msg91.com/api/v5/email/send",
      data,
      {
        headers: {
          accept: "application/json",
          authkey: `${SECRETS.msgAuthKey}`,
          "content-type": "application/json",
        },
      }
    );
    // console.log(JSON.stringify(response.data));
  } catch (error) {
    // console.error(JSON.stringify(error));
  }
};

// await sendSms("664ed8d0d6fc05475e4cdbe2","1", "8178813400", null, null);

// await sendEmail(
//   "kushagra",
//   "<EMAIL>",
//   "sign_up_3",
//   `{"User": "kushagra"}`
// );
