import jwt from "jsonwebtoken";
import { SECRETS } from "./config.js";

export const newToken = (user, userType) => {
  return jwt.sign(
    {
      id: user._id,
      name: user.firstName,
      image: user?.profileImg ? user.profileImg : undefined,
      userType,
    },
    SECRETS.jwt,
    {
      expiresIn: SECRETS.jwtExp,
    }
  );
};

export const verifyToken = (token) =>
  new Promise((resolve, reject) => {
    jwt.verify(token, SECRETS.jwt, (err, payload) => {
      if (err) return reject(err);
      resolve(payload);
    });
  });
