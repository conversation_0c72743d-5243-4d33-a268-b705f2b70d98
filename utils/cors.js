import cors from "cors";

const corsOptions = {
  origin: [
    "http://localhost:3002/",
    "http://localhost:3001",
    "http://localhost:3000",
    "http://localhost:3003",
    "http://localhost:3002",
    "https://sxwcvemsqf.execute-api.ap-south-1.amazonaws.com/",
    "https://sxwcvemsqf.execute-api.ap-south-1.amazonaws.com",
    "https://kdfqfiggp1.execute-api.ap-south-1.amazonaws.com/",
    "https://kdfqfiggp1.execute-api.ap-south-1.amazonaws.com",
    "https://hdorno8zdg.execute-api.ap-south-1.amazonaws.com/",
    "https://hdorno8zdg.execute-api.ap-south-1.amazonaws.com",
    "https://eysjvfcbfa.execute-api.ap-south-1.amazonaws.com/",
    "https://eysjvfcbfa.execute-api.ap-south-1.amazonaws.com",
    "https://uatacademy.khelcoach.com/",
    "https://uatacademy.khelcoach.com",
    "https://uatcoach.khelcoach.com/" ,
    "https://uatcoach.khelcoach.com",
    "https://uatadmin.khelcoach.com/",
    "https://uatadmin.khelcoach.com",
    "https://uat.khelcoach.com/",
    "https://uat.khelcoach.com",
  ],
  methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization"],
  credentials: true,
};

export default cors(corsOptions);
