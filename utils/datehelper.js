import moment from "moment";
export function formatDateToYYYYMMDD(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2); // Add leading zero if needed
    const day = `0${date.getDate()}`.slice(-2); // Add leading zero if needed
    return `${year}-${month}-${day}`;
  }



  export function daysDifference(startDate, endDate, daysArray) {
    const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    let count = 0;
    let currentDate = new Date(start);

    while (currentDate <= end) {
        if (daysArray.includes(daysOfWeek[currentDate.getDay()])) {
            count++;
        }
        currentDate.setDate(currentDate.getDate() + 1);
    }

    return count;
  }
  