import { Categories } from "./categoriesModal.js";
import { multerUpload } from "../utils/awsHelper.js";
import { SECRETS } from "../utils/config.js";
import validator from "validator";
import AWS from "aws-sdk";

const AWS_ACCESS_KEY_ID = SECRETS.AWS_ACCESS_KEY_ID;
const AWS_SECRET_ACCESS_KEY = SECRETS.AWS_SECRET_ACCESS_KEY;
const AWS_BUCKET_NAME = SECRETS.AWS_BUCKET_NAME;
const AWS_S3_REGION = SECRETS.AWS_S3_REGION;

AWS.config.update({
  accessKeyId: AWS_ACCESS_KEY_ID,
  secretAccessKey: AWS_SECRET_ACCESS_KEY,
  region: AWS_S3_REGION,
});
const s3 = new AWS.S3();

export const getCategories = async (req, res) => {
  try {
    let { page, name } = req.query;
    page = page || 1;
    let limitVal = 25;
    let skipValue = (page - 1) * limitVal;
    let query = {};
    if (name) {
      query.name = { $regex: name, $options: "i" };
    }
    const totalResults = await Categories.countDocuments(query);
    const data = await Categories.find(query)
      .skip(skipValue)
      .limit(limitVal)
      .sort({ createdAt: -1 });
    return res.status(200).json({ data, totalResults });
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const getCategoryById = async (req, res) => {
  try {
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "id is required" });
    }
    const data = await Categories.findById(id);
    if (!data) {
      return res.status(400).json({ error: "Category Not found" });
    }
    return res.status(200).json(data);
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const createCategory = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { name, handle, description, image } = req.body;
    if (!name || !handle || !image) {
      return res
        .status(400)
        .json({ error: "name, handle and image are required" });
    }
    if (!validator.isLength(name, { min: 3 })) {
      return res
        .status(400)
        .json({ error: "Category name must have at least 3 characters" });
    }

    if (!validator.isLength(handle, { min: 3 })) {
      return res
        .status(400)
        .json({ error: "Handle must have at least 3 characters" });
    }
    const category = await Categories.findOne({ name });
    if (category) {
      return res.status(400).json({ error: "Category already exists" });
    }
    const lowerCaseName = name.toLowerCase();
    const data = await Categories.create({
      name,
      description,
      handle,
      image,
      lowerCaseName,
    });
    return res.status(200).json(data);
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const updateCategory = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "id is required" });
    }
    const { name, description, image } = req.body;
    if (name && !validator.isLength(name, { min: 3 })) {
      return res
        .status(400)
        .json({ error: "Category name must have at least 3 characters" });
    }

    if (description && !validator.isLength(description, { min: 10 })) {
      return res
        .status(400)
        .json({ error: "Description must have at least 10 characters" });
    }
    const category = await Categories.findOne({
      _id: { $ne: id },
      lowerCaseName: name?.toLowerCase(),
    });
    if (category) {
      return res.status(400).json({ error: "Category already exists" });
    }
    const updateObj = {
      ...req.body,
    };
    const data = await Categories.findByIdAndUpdate(id, updateObj, {
      new: true,
    });
    return res.status(200).json(data);
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const deleteCategory = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "id is required" });
    }
    const data = await Categories.findByIdAndDelete(id);
    if (!data) {
      return res.status(400).json({ error: "Category not found" });
    }
    return res.status(200).json({ message: "Deleted Successfully" });
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const uploadFile = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const uploadSingle = multerUpload(
      AWS_BUCKET_NAME,
      "images2/categories"
    ).single("image");

    uploadSingle(req, res, async (err) => {
      if (err) {
        return res.status(400).json({ error: err.message });
      }
      const url = req.body.url;
      if (url) {
        const deleteParams = {
          Bucket: AWS_BUCKET_NAME,
          Key: `images2/categories/${url.split("/").pop()}`,
        };
        const deleteResult = await s3.deleteObject(deleteParams).promise();
      }
      // The uploaded file URL directly from S3
      const imageUrl = req?.file?.location;

      return res.status(200).json({ url: imageUrl });
    });
  } catch (error) {
    return res.status(500).json({
      error: error.message || "Internal server error",
    });
  }
};
