import bcrypt from "bcryptjs";
import User from "./userModal.js";
import jwt from "jsonwebtoken";
import { authenticate } from "../../Helpers/authenticate.js";
import { SECRETS } from "../../utils/config.js";

// Get all users
async function readAllUsers(req, res) {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let users = await User.find({});
    let updated = users.map((user) => {
      delete user.password;
      return user;
    });
    return res.status(200).json(updated);
  } catch (err) {
    return res.status(500).json({ err: err.message });
  }
}

// Create a new user
async function createUser(req, res) {
  try {
    const { name, username, password, userGroup } = req.body;

    let hashedPassword = password;
    if (password) {
      hashedPassword = await bcrypt.hash(password, 10);
    }

    const user = new User({
      name,
      username,
      password: hashedPassword,
      userGroup,
    });

    const savedUser = await user.save();
    delete savedUser.password;
    return res.status(201).json(savedUser);
  } catch (error) {
    return res.status(500).json({ err: error.message });
  }
}

// Get a user by username
async function getUserById(req, res) {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { id } = req.params;

    const user = await User.findById(id);

    if (!user) {
      return res.status(404).json({ err: "User not found" });
    }
    delete user.password;
    return res.json(user);
  } catch (error) {
    return res.status(500).json({ err: error.message });
  }
}

// Update a user by username
async function updateUserById(req, res) {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { id } = req.params;

    if (id === "648bed7c88770c2830edb278") {
      throw new Error("Can't update Super Admin");
    }

    if (req.body.password) {
      if (req.body.password.length >= 6) {
        const hashedPassword = await bcrypt.hash(req.body.password, 10);
        req.body.password = hashedPassword;
      } else {
        return res
          .status(400)
          .json({ err: "Password (at least 6 characters)" });
      }
    }

    if (req.body.userGroup && req.body.userGroup.length === 0) {
      return res.status(400).json({ err: "Select at least one role" });
    }

    if (Object.keys(req.body).includes("name") && req.body.name.length < 3) {
      return res
        .status(400)
        .json({ err: "Name should be of at least 3 characters" });
    }

    if (
      Object.keys(req.body).includes("username") &&
      req.body.username.length < 6
    ) {
      return res
        .status(400)
        .json({ err: "Username should be of at least 6 characters" });
    }

    const updatedUser = await User.findByIdAndUpdate(id, req.body, {
      new: true,
    });

    if (!updatedUser) {
      return res.status(404).json({ err: "User not found" });
    }
    delete updatedUser.password;
    return res.json(updatedUser);
  } catch (error) {
    return res.status(500).json({ err: error.message });
  }
}

// Delete a user by username
async function deleteUserById(req, res) {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { id } = req.params;

    if (id === process.env.SUPER_ADMIN) {
      throw new Error("Can't delete Super Admin");
    }

    const deletedUser = await User.findByIdAndDelete(id);

    if (!deletedUser) {
      return res.status(404).json({ err: "User not found" });
    }

    return res.status(200).json({ success: "User Deleted Successfully" });
  } catch (error) {
    return res.status(500).json({ err: error.message });
  }
}

// Controller function for user login
async function loginUser(req, res) {
  const { username, password } = req.body;
  try {
    const user = await authenticate(username, password, "user");
    if (!user) {
      return res.status(401).json({ err: "Invalid username or password" });
    }

    const token = jwt.sign(
      {
        userId: user._id,
        username: user.username,
        name: user.name,
        userType: "Admin",
      },
      SECRETS.jwt,
      { expiresIn: "8h" }
    );

    return res.json({ token, user });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ err: "Internal server error" });
  }
}

export {
  readAllUsers,
  createUser,
  getUserById,
  updateUserById,
  deleteUserById,
  loginUser,
};
