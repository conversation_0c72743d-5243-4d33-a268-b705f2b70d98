import mongoose from "mongoose";

const UserSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, "Name required"],
    validate: {
      validator: function (name) {
        return name.trim().length > 2;
      },
      message: "Name should be of at least 3 characters",
    },
  },
  username: {
    type: String,
    required: [true, "Username/Email required"],
    unique: [true, "Username/Email ID already exists"],
    validate: {
      validator: function (name) {
        return name.trim().length > 4;
      },
      message: "Username should be of at least 5 characters",
    },
  },
  password: {
    type: String,
    required: [true, "Password required"],
    validate: {
      validator: function (pass) {
        return pass.trim().length > 5;
      },
      message: "Password should be of at least 6 characters",
    },
  },
  userGroup: {
    type: Array,
    required: [true, "User must be associated to at leat 1 group"],
    validate: {
      validator: function (array) {
        return array.length > 0;
      },
      message: "User must be associated to at leat 1 group",
    },
  },
});

const User = mongoose.model("User", UserSchema);

export default User;