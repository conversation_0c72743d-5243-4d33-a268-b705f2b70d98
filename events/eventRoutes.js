import { Router } from "express";

import {
  createEvent,
  deleteEvent,
  getEventById,
  getEvents,
} from "./eventController.js";
import { coachProtect } from "../utils/auth.js";

const router = Router();

router.route("/").get(coachProtect, getEvents).post(coachProtect, createEvent);
router
  .route("/:id")
  .get(coachProtect, getEventById)
  .delete(coachProtect, deleteEvent);

export default router;
