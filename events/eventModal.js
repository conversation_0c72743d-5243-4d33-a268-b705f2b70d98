import mongoose from "mongoose";
const { Schema, model } = mongoose;

const eventSchema = new Schema(
  {
    eventId: {
      type: String,
    },
    name: {
      type: String,
      required: true,
    },
    coachId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
    },
    startTime: {
      type: String,
      required: true,
    },
    endTime: {
      type: String,
      required: true,
    },
    days: [{ type: String, required: true }],
    dates: [
      {
        type: Date,
      },
    ],
  },
  { timestamps: true }
);

export const Events = model("events", eventSchema);
